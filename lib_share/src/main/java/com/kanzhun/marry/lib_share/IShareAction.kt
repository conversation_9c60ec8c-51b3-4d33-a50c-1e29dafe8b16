package com.kanzhun.marry.lib_share

import com.kanzhun.marry.lib_share.bean.ProfileShareBean
import com.kanzhun.marry.lib_share.core.SharePlatForm

interface IShareAction {
    fun share(userId:String,shareType:String)

    fun shareWithBean(bean: ProfileShareBean)

    interface Callback {
        fun onStart(type: SharePlatForm?, subType: Int, desc: String?)
        fun onComplete(type: SharePlatForm?, success: Boolean, subType: Int, desc: String?)
    }
}