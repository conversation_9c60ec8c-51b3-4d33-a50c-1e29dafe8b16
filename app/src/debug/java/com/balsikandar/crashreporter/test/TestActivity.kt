@file:OptIn(ExperimentalMaterial3Api::class)

package com.balsikandar.crashreporter.test

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.amap.api.maps.MapsInitializer
import com.kanzhun.marry.R
import com.kit.baselibrary.IExtraBean

class TestActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        MapsInitializer.updatePrivacyAgree(this, true)
        MapsInitializer.updatePrivacyShow(this, true, true)

        super.onCreate(savedInstanceState)
        setContent {
            Content()
        }
    }

    @Composable
    private fun Content(
        modifier: Modifier = Modifier,
        viewModel: TestViewModel = viewModel()
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = colorResource(R.color.common_color_191919),
                        titleContentColor = Color.White,
                    ),
                    title = {
                        Text("测试")
                    }
                )
            },
        ) { innerPadding ->
            val kits by viewModel.kits.observeAsState(emptyList())
            LazyColumn(modifier = modifier.padding(innerPadding)) {
                items(items = kits) {
                    ClickableKitItem(it)

                    HorizontalDivider()
                }
            }
        }
    }

    @Composable
    private fun ClickableKitItem(item: IExtraBean) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    item.onClick(this@TestActivity)
                }
                .padding(16.dp)
        ) {
            Text(
                text = item.getName(),
                style = TextStyle(
                    fontWeight = FontWeight(400),
                    color = Color.Black
                ),
                modifier = Modifier
            )
        }
    }

    @Preview(showSystemUi = true)
    @Composable
    private fun PreviewContent() {
        Content()
    }
}
