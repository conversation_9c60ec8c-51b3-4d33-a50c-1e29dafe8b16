package com.kanzhun.marry.alphaplayerdemo.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/16
 */
public class ConfigModel implements Serializable {

    private static final long serialVersionUID = 1466117793319904136L;
    @SerializedName("landscape")
    public Item landscapeItem;
    @SerializedName("portrait")
    public Item portraitItem;

    public Item getLandscapeItem() {
        return landscapeItem;
    }

    public void setLandscapeItem(Item landscapeItem) {
        this.landscapeItem = landscapeItem;
    }

    public Item getPortraitItem() {
        return portraitItem;
    }

    public void setPortraitItem(Item portraitItem) {
        this.portraitItem = portraitItem;
    }

    public static class Item implements Serializable {
        private static final long serialVersionUID = 1144447177237189706L;
        @SerializedName("path")
        public String path;
        @SerializedName("align")
        public int alignMode;

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public int getAlignMode() {
            return alignMode;
        }

        public void setAlignMode(int alignMode) {
            this.alignMode = alignMode;
        }
    }
}
