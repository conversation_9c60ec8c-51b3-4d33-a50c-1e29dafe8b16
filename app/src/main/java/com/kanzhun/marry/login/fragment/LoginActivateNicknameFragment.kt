package com.kanzhun.marry.login.fragment

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.activity.OnBackPressedCallback
import androidx.navigation.Navigation.findNavController
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentLoginActivateNicknameBinding
import com.kanzhun.foundation.utils.LogoutUtil
import com.kanzhun.marry.me.views.LoginNextPageView
import com.kanzhun.utils.T
import com.kanzhun.common.kotlin.ui.onClick
import com.qmuiteam.qmui.util.QMUIKeyboardHelper

class LoginActivateNicknameFragment :
    LoginActivateBaseFragment<FragmentLoginActivateNicknameBinding>() {

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        QMUIKeyboardHelper.showKeyboard(mBinding.editText, true)
        context?.color(R.color.common_color_191919)
            ?.let { mBinding.viewEditLine.setBackgroundColor(it) }
        mBinding.tvErrorDesc.text = ""
        mBinding.tvErrorDesc.gone()
        activityViewModel.nameErrorLiveData.observe(this) {
            if (it == null) {
                return@observe
            }
            mBinding.btnNext.setState(LoginNextPageView.STATE.ERROR)
            context?.color(R.color.common_color_FF3F4B)
                ?.let { mBinding.viewEditLine.setBackgroundColor(it) }
            mBinding.tvErrorDesc.text = it.second
            mBinding.tvErrorDesc.visible()
        }
        activityViewModel.activateSuccessLivaData.observe(this) {
            if (it == 1) {
                goNext()
            }
        }
        mBinding.btnNext.setState(LoginNextPageView.STATE.IDLE)
        mBinding.btnNext.setAnimClickListener() {
            val name = mBinding.editText.text.toString()
            if (name.none()) {
                return@setAnimClickListener
            }
            reportClick(mBinding.tvPhoneTitle.getTitle())
//            if(name == activityViewModel.user?.nickName){
//                activityViewModel.activateSuccessLivaData.postValue(1)
//            }else{
                activityViewModel.saveName(name)
//            }
        }
        mBinding.tvPhoneTitle.setRightSkip {
            goNext()
            reportSkip(mBinding.tvPhoneTitle.getTitle())
        }

        mBinding.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                context?.color(R.color.common_color_191919)
                    ?.let { mBinding.viewEditLine.setBackgroundColor(it) }
                mBinding.tvErrorDesc.text = ""
                mBinding.tvErrorDesc.gone()
                activityViewModel.nameErrorLiveData.value = null
            }

            override fun afterTextChanged(s: Editable) {
                if (s.toString().isNotEmpty() && s.toString().length > 10) {
                    T.ss("昵称不能超过10个字哦")
                    mBinding.editText.setText(s.substring(0, 10))
                    mBinding.editText.setSelection(mBinding.editText.text.length)
                }
                mBinding.btnNext.enable((s.trim()).isNotEmpty())
            }
        })
        activityViewModel.user?.nickName?.apply {
            mBinding.editText.setText(this)
        }

        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
//                    LoginPageRouter.jumpToSelectModel(
//                        context,
//                        false,
//                        ActivityAnimType.SLIDE_FROM_LEFT_TO_RIGHT
//                    )
//                    AppUtil.finishActivityDelay(activity)
                    LogoutUtil.logoutDialog(context)
                }
            })
    }

    private fun goNext() {
        activityViewModel.activateSuccessLivaData.value = 0
        QMUIKeyboardHelper.hideKeyboard(mBinding.editText)
        findNavController(mBinding.btnNext).navigate(R.id.action_nickNameFragment_to_sexFragment)
    }


    override fun initData() {
        reportExp(mBinding.tvPhoneTitle.getTitle())
    }

    override fun onRetry() {
    }

    override fun getStateLayout(): StateLayout? = null
}