package com.kanzhun.marry.login.activity

import android.content.Intent
import androidx.navigation.NavController
import androidx.navigation.NavGraph
import androidx.navigation.findNavController
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.constract.LivedataKeyTask
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ui.statusbar.immersive
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.foundation.router.MainPageRouter
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.ActivityLoginSkipBinding
import com.kanzhun.marry.login.viewmodel.LoginSkipViewModel
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [LoginPageRouter.LOGIN_SKIP_ACTIVITY])
class LoginSkipActivity :
    BaseBindingActivity<ActivityLoginSkipBinding, LoginSkipViewModel>() {
//    private var gotoComplete: Boolean = false
    override fun preInit(intent: Intent) {
//        gotoComplete = intent.getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN, false);

        immersive(darkMode = true)
    }

    override fun initView() {
//        if (gotoComplete) {
//            val navController: NavController = findNavController(R.id.fragment)
//            val navGraph:NavGraph =  navController.navInflater.inflate(R.navigation.login_activate_skip)
//            navGraph.setStartDestination(R.id.completeFragment)
//            navController.graph = navGraph
//        }
        initObserver()
    }

    override fun onBackPressed() {
        gotoMain()
    }

    private fun initObserver() {
        mViewModel.skipSuccessLivaData.observe(this) {
            if (it == -1) {
                gotoMain()
            }
        }

        //实名认证成功
        liveEventBusObserve(LivedataKeyTask.NEW_USER_TASK_REAL_USER_FINISH) { it: String ->
            when (it) {
                CertificationIdentifySource.LOGIN_VERIFIED -> {
                    gotoMain()
                }
            }
        }
    }

    private fun gotoMain() {
        //去主页面
        MainPageRouter.jumpToMainActivity(this)
        AppUtil.finishActivity(this@LoginSkipActivity)
    }

    override fun initData() {

    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null
}