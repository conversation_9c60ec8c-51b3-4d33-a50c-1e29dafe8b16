package com.kanzhun.marry.login.activity

import android.content.Intent
import androidx.navigation.NavController
import androidx.navigation.NavGraph
import androidx.navigation.findNavController
import com.sankuai.waimai.router.annotation.RouterUri
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.ActivityLoginActivateBinding
import com.kanzhun.marry.login.viewmodel.LoginActivateViewModel

@RouterUri(path = [LoginPageRouter.LOGIN_ACTIVATE_ACTIVITY])
class LoginActivateActivity :
    BaseBindingActivity<ActivityLoginActivateBinding, LoginActivateViewModel>() {
    override fun preInit(intent: Intent) {
        mViewModel.type = intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0)
        if (mViewModel.type == 1 || mViewModel.type == 2) {
            val navController: NavController = findNavController(R.id.fragment)
            val navGraph: NavGraph =  navController.navInflater.inflate(R.navigation.login_activate_navigation)
            navGraph.setStartDestination(R.id.keywordFragment)
            navController.graph = navGraph

            val array = intent.getIntegerArrayListExtra(BundleConstants.BUNDLE_DATA_INT_ARRAY)
            array?.forEach {
                mViewModel.array.add(it)
            }

        }else{
            mViewModel.getData()
        }
        liveEventBusObserve("other_platform_complete"){ it:Boolean->
            gotoSkip()
        }
    }

    override fun initView() {
        initObserver()

    }

    private fun initObserver() {
        mViewModel.activateSuccessLivaData.observe(this) {
            if (it == -1) {
                gotoSkip()
            }
        }
    }

    private fun gotoSkip() {
        LoginPageRouter.jumpToSkipActivity(this@LoginActivateActivity,false)
        AppUtil.finishActivityDelay(this@LoginActivateActivity)
    }

    override fun initData() {

    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null
}