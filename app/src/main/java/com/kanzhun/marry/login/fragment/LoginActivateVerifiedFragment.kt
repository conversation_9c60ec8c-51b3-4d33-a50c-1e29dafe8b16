package com.kanzhun.marry.login.fragment

import android.os.Bundle
import androidx.navigation.Navigation
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.common.kotlin.ui.statusbar.addStatusPadding
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.login.point.LoginPointAction
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentLoginActivateVerifiedBinding

/**
 * 首善—实名认证
 */
class LoginActivateVerifiedFragment :
    LoginSkipBaseFragment<FragmentLoginActivateVerifiedBinding>() {

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.idRoot.addStatusPadding()
        mBinding.icTitle.setLeftIconGone()
        mBinding.idPassBtn.onClick {
            activityViewModel.skipSuccessLivaData.postValue(-1)
            reportPoint(LoginPointAction.CHILD_FIRSTCOMPLETE_PAGE_SKIP){
                step = "实名认证"
            }
        }
        mBinding.idSureBtn.onClick {
            MePageRouter.jumpToCertificationActivity(context,CertificationIdentifySource.LOGIN_VERIFIED, pageSource = PageSource.FIRST_NAME_VERIFIED_FRAGMENT)
            reportPoint(LoginPointAction.CHILD_FIRSTCOMPLETE_REALNAME_BEGINCLICK)
        }

    }

    override fun initData() {

    }

    override fun onResume() {
        super.onResume()
        reportPoint(LoginPointAction.CHILD_FIRSTCOMPLETE_PAGE_EXPO){
            step = "实名认证"
        }
    }

    override fun onRetry() {
    }


    override fun getStateLayout(): StateLayout? = null
}