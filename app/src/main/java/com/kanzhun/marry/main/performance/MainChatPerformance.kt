package com.kanzhun.marry.main.performance

import androidx.lifecycle.LifecycleOwner
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.logic.mqtt.MMSStartup

class MainChatPerformance:AbsPerformance() {
    private var mMqttStart: Boolean? = null


    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        if (mMqttStart == null) {
            mMqttStart = false
        } else if (!mMqttStart!!) {
            mMqttStart = true
            MMSStartup.startLifecycle()
        }
    }
}