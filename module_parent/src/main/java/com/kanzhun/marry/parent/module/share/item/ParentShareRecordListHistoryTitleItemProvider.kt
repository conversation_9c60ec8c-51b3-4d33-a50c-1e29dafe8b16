package com.kanzhun.marry.parent.module.share.item

import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.marry.parent.databinding.ParentShareRecordHistoryTitleBinding

class ParentShareRecordListHistoryTitleItemProvider : BaseItemProvider<BaseListItem, ParentShareRecordHistoryTitleBinding>() {
    override fun onBindItem(binding: ParentShareRecordHistoryTitleBinding, item: BaseListItem) {

    }


}