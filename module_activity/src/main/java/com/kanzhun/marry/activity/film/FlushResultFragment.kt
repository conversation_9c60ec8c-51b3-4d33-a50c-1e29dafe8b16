package com.kanzhun.marry.activity.film

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.annotation.IdRes
import com.kanzhun.common.base.AllBaseActivity
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.common.kotlin.ui.statusbar.addStatusPadding
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.marry.activity.databinding.ActivityFlushResultFailedBinding
import com.kanzhun.marry.activity.databinding.ActivityFlushResultPicBinding
import com.kanzhun.marry.activity.databinding.ActivityFlushResultTxtBinding
import com.kanzhun.marry.activity.databinding.ActivityFragmentFlushResultBinding
import com.kanzhun.marry.fragment.FlushedLostFailedScene
import com.kanzhun.marry.fragment.FlushedPicScene
import com.kanzhun.marry.fragment.FlushedTxtScene
import com.kanzhun.marry.fragment.ReceivedPicScene
import com.kanzhun.marry.fragment.ReceivedTxtScene
import com.kanzhun.marry.fragment.Scene

/**
 * 胶片冲洗结果页面
 */
class FlushResultFragment :
    BaseBindingFragment<ActivityFragmentFlushResultBinding, FlushResultViewModel>() {
    companion object {
        private const val TAG = "FlushResultFragment"

        fun show(activity: AllBaseActivity, @IdRes containerViewId: Int) {
            activity.supportFragmentManager.beginTransaction()
                .add(
                    containerViewId,
                    FlushResultFragment(),
                    TAG
                )
                .commitAllowingStateLoss()

        }
    }

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.llContentRoot.addStatusPadding()
        mBinding.run {
            ivClose.setOnClickListener {
                activity?.supportFragmentManager?.let { sfm ->
                    sfm.findFragmentByTag(TAG)?.let { f ->
                        sfm.beginTransaction().remove(f).commitAllowingStateLoss()
                    }
                }
            }
        }
    }

    override fun initData() {

    }

    override fun onRetry() {
    }

    override fun getStateLayout(): StateLayout? = null
}

