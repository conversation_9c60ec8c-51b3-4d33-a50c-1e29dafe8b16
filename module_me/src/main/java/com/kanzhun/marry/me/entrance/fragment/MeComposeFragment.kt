package com.kanzhun.marry.me.entrance.fragment

import android.content.Context
import android.text.TextUtils
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.BaseComposeFragment
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.common.util.ProtocolHelper.Companion.parseProtocol
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.ktx.simplePost
import com.kanzhun.foundation.model.profile.UserTabModel
import com.kanzhun.foundation.model.profile.UserTabModel.MenuBean
import com.kanzhun.foundation.router.CommonPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.SocialPageRouter
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.entrance.viewmodel.MeComposeViewModel
import com.kanzhun.marry.me.entrance.viewmodel.mockOfficeActivityBean
import com.kanzhun.marry.me.entrance.viewmodel.mockUserTabModel
import com.kanzhun.marry.me.entrance.views.F4ActivityCard
import com.kanzhun.marry.me.entrance.views.F4ActivityCardNormal
import com.kanzhun.marry.me.entrance.views.MeHeader
import com.kanzhun.utils.T

class MeComposeFragment : BaseComposeFragment() {
    @Composable
    override fun OnSetContent() {
        val phone = SpManager.get().user().getString(Constants.CUSTOM_SERVICE_KEY, "")
        MeComposeFragmentContent(content = phone)
    }
}

@Composable
fun MeComposeFragmentContent(
    modifier: Modifier = Modifier,
    meComposeViewModel: MeComposeViewModel = viewModel(),
    inPreview: Boolean = false,
    content: String? = "客服电话",
) {
    val context = LocalContext.current
    val userTabModel = meComposeViewModel.userTabModel
    val officeActivityBean = meComposeViewModel.officeActivityBean

    val lifecycleOwner = LocalLifecycleOwner.current

    DisposableEffect(lifecycleOwner) {
        val observer = object : androidx.lifecycle.LifecycleEventObserver {
            override fun onStateChanged(
                source: androidx.lifecycle.LifecycleOwner,
                event: Lifecycle.Event,
            ) {
                if (event == Lifecycle.Event.ON_RESUME) {
                    meComposeViewModel.refreshData()
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    if (userTabModel == null)
        return

    Box(
        modifier = modifier
            .background(color = Color(0xFFF5F5F5))
            .fillMaxSize() // 新增此行
            .padding(start = 12.dp, end = 12.dp, bottom = 25.dp)
    ) {

        Column(
            Modifier
                .verticalScroll(rememberScrollState()) // 添加滚动
                .windowInsetsPadding(WindowInsets.statusBars)
        ) {

            meetingPlanTabMyTopBar()


            MeHeader(
                userTabModel = userTabModel,
                onClickAvatar = {
                    clickEditInfo(userTabModel?.apply {
                        index = 1
                        isNewPage = true
                    }, context)

                    reportPoint("F4-avatar-click")
                },
                onClickEditor = {
                    clickEditInfo(userTabModel?.apply {
                        index = 0
                        isNewPage = true
                    }, context)
                },
                onClickMood = {
                    if (userTabModel == null) return@MeHeader
                    if (userTabModel.moodInfo == null || (userTabModel.moodInfo.encMoodId
                            ?: "").isEmpty()
                    ) {
                        MePageRouter.jumpToMoodGridActivity(
                            context,
                            PageSource.F4_ME_CHILD_FRAGMENT,
                            null
                        );
                    } else {
                        if (userTabModel.moodInfo.rejectReason?.isNotEmpty() == true) {
                            MePageRouter.jumpToMoodEditActivity(
                                context,
                                PageSource.ME_MOOD_REJECT,
                                null
                            );
                        } else {
                            MePageRouter.jumpToMyMoodDetailActivity(
                                context,
                                PageSource.F4_ME_CHILD_FRAGMENT
                            );
                        }
                    }
                }
            )

            if ((userTabModel?.baseInfo?.unPassCertCount ?: 0) > 0) {
                meetingPlanErrorTip("资料内容未通过审核", onClick = {
                    clickEditInfo(userTabModel?.apply {
                        index = 0
                        isNewPage = true
                    }, context)
                })
            }

            if (userTabModel.meetupCard != null && !TextUtils.isEmpty(userTabModel.meetupCard.picUrl)){
                AsyncImage(
                    model = userTabModel.meetupCard?.picUrl?:"",
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(351 / 123f)
                        .clip(RoundedCornerShape(16.dp))
                        .noRippleClickable {
                            parseProtocol(userTabModel.meetupCard.protocol)
                        },
                    contentScale = ContentScale.FillBounds,
                )
                Spacer(modifier = Modifier.height(16.dp))
            }else if (officeActivityBean != null && officeActivityBean.activityList.isNotEmpty()){
                if (officeActivityBean.activityList.any { it.activityRelationStatus == 8 && System.currentTimeMillis() < it.endTime }){
                    F4ActivityCard(officeActivityBean.activityList.filter { it.activityRelationStatus == 8 && System.currentTimeMillis() < it.endTime })
                }else{
                    if (userTabModel?.todoInfo != null) {
                        CertCard(
                            title = if (userTabModel?.showNoviceTask == 1) "新手任务" else if (userTabModel?.showUndoCert == 1) "完成更多认证信息可提高曝光" else "",
                            showRightMore = userTabModel?.showNoviceTask == 1,
                            url = userTabModel?.todoInfo?.icon,
                            subTitle = userTabModel?.todoInfo?.title,
                            subContent = userTabModel?.todoInfo?.content,
                            btnTxt = userTabModel?.todoInfo?.button?.text,
                            btnStyle = userTabModel?.todoInfo?.status ?: 0,
                            onMoreClick = {
                                if (context is FragmentActivity) {
                                    MePageRouter.showNewUserTaskBlockDialog(context)
                                }
                            }, onClick = {
                                if (userTabModel?.todoInfo?.status == 2) {
                                    return@CertCard
                                }
                                if (userTabModel?.todoInfo?.status == 1) {
                                    T.ss("资料审核中，请耐心等待")
                                    return@CertCard
                                }
                                ProtocolHelper.parseProtocol(userTabModel?.todoInfo?.button?.url)
                            })

                        Spacer(modifier = Modifier.height(12.dp))
                    }

                    F4ActivityCardNormal(officeActivityBean.activityList)
                }

                Spacer(modifier = Modifier.height(16.dp))
            }else{
                if (userTabModel?.todoInfo != null) {
                    CertCard(
                        title = if (userTabModel?.showNoviceTask == 1) "新手任务" else if (userTabModel?.showUndoCert == 1) "完成更多认证信息可提高曝光" else "",
                        showRightMore = userTabModel?.showNoviceTask == 1,
                        url = userTabModel?.todoInfo?.icon,
                        subTitle = userTabModel?.todoInfo?.title,
                        subContent = userTabModel?.todoInfo?.content,
                        btnTxt = userTabModel?.todoInfo?.button?.text,
                        btnStyle = userTabModel?.todoInfo?.status ?: 0,
                        onMoreClick = {
                            if (context is FragmentActivity) {
                                MePageRouter.showNewUserTaskBlockDialog(context)
                            }
                        }, onClick = {
                            if (userTabModel?.todoInfo?.status == 2) {
                                return@CertCard
                            }
                            if (userTabModel?.todoInfo?.status == 1) {
                                T.ss("资料审核中，请耐心等待")
                                return@CertCard
                            }
                            ProtocolHelper.parseProtocol(userTabModel?.todoInfo?.button?.url)
                        })

                    Spacer(modifier = Modifier.height(12.dp))
                }
            }



            MeetingPlanTabMyBottomCard(viewModel = meComposeViewModel, inPreview = inPreview)

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = content ?: "",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFB7B7B7),
                ),
                textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview
@Composable
fun PreviewMeComposeFragment() {
    val viewModel: MeComposeViewModel =
        viewModel(factory = MeComposeViewModel.Factory(inPreview = true))
    viewModel.userTabModel = mockUserTabModel
    viewModel.officeActivityBean = mockOfficeActivityBean

    MeComposeFragmentContent()
}


@Composable
private fun meetingPlanErrorTip(str: String, onClick: () -> Unit = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFFFD2D9), shape = RoundedCornerShape(12.dp))
            .padding(horizontal = 12.dp, vertical = 10.dp)
            .noRippleClickable {
                onClick()
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.mipmap.common_ic_warning),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = str,
            style = TextStyle(fontSize = 13.sp, fontWeight = FontWeight.Bold),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = "去修改",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFFF3F4B)
            )
        )
    }
    Spacer(modifier = Modifier.height(10.dp))
}

@Composable
private fun meetingPlanTabMyTopBar() {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .height(44.dp)
            .fillMaxWidth()
    ) {

        Row(
            modifier = Modifier
                .align(Alignment.CenterEnd)
        ) {

            Image(
                painter = painterResource(R.mipmap.me_ic_icon_scan_qr_code),
                modifier = Modifier
                    .size(24.dp)
                    .noRippleClickable {
                        CommonPageRouter.Companion.jumpToScanActivity(
                            context,
                            PageSource.F4_ME_CHILD_FRAGMENT,
                            "",
                            "",
                            0
                        )
                    },
                contentDescription = "",
            )
        }

    }
}

@Composable
fun CertCard(
    title: String? = "新手任务",
    showRightMore: Boolean = false,
    url: String? = "",
    subTitle: String? = "",
    subContent: String? = "",
    btnTxt: String? = "查看",
    btnStyle: Int = 0,
    onMoreClick: () -> Unit, onClick: () -> Unit,
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(123.dp)
            .border(2.dp, Color(0xFFE5E5E5), shape = RoundedCornerShape(12.dp))
            .clip(RoundedCornerShape(12.dp))
            .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(12.dp))
            .noRippleClickable {
                onClick()
            }
    ) {

        val (refTitle, refMore, refIcon, refImage, refItemTitle, refItemContent, refItemBtn) = createRefs()

        Image(
            painter = painterResource(id = R.mipmap.img_me_icon_task_bg),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxSize()
        )


        Text(
            text = title ?: "",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF191919),
            ),
            modifier = Modifier
                .constrainAs(refTitle) {
                    top.linkTo(parent.top, 12.dp)
                    start.linkTo(parent.start, 16.dp)
                }
        )

        if (showRightMore) {
            Text(
                text = "查看更多",
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF5E5E5E),
                ),
                modifier = Modifier
                    .noRippleClickable {
                        onMoreClick()
                    }
                    .constrainAs(refMore) {
                        end.linkTo(refImage.start, 6.dp)
                        top.linkTo(parent.top, 13.dp)
                    }
            )

            Image(
                painter = painterResource(id = R.drawable.me_ic_right),
                contentDescription = "image description",
                contentScale = ContentScale.None,
                modifier = Modifier
                    .size(4.5.dp, 9.dp)
                    .constrainAs(refImage) {
                        end.linkTo(parent.end, 12.dp)
                        top.linkTo(refMore.top)
                        bottom.linkTo(refMore.bottom)
                    }
            )
        }



        AsyncImage(
            model = url,
            contentDescription = "image description",
            contentScale = ContentScale.None,
            modifier = Modifier
                .size(40.dp)
                .constrainAs(refIcon) {
                    start.linkTo(parent.start, 16.dp)
                    top.linkTo(refItemTitle.top)
                    bottom.linkTo(refItemContent.bottom)
                },

            )


        Text(
            text = subTitle ?: "",
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF191919),
            ),
            modifier = Modifier
                .constrainAs(refItemTitle) {
                    start.linkTo(refIcon.end, 12.dp)
                    top.linkTo(refTitle.bottom, 24.dp)
                }
        )

        Text(
            text = subContent ?: "",
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF5E5E5E),
            ),
            modifier = Modifier
                .constrainAs(refItemContent) {
                    start.linkTo(refIcon.end, 12.dp)
                    top.linkTo(refItemTitle.bottom, 4.dp)
                }
        )

        if (btnStyle == 0) {
            Box(
                modifier = Modifier
                    .background(color = Color(0xFF292929), shape = RoundedCornerShape(16.dp))
                    .padding(horizontal = 12.dp, vertical = 5.dp)
                    .noRippleClickable {
                        onClick()
                    }
                    .constrainAs(refItemBtn) {
                        end.linkTo(parent.end, 12.dp)
                        top.linkTo(refIcon.top, 7.dp)
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = btnTxt ?: "",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    ),
                )
            }
        } else if (btnStyle == 1) {
            Box(
                modifier = Modifier
                    .background(color = Color(0xFFebebeb), shape = RoundedCornerShape(16.dp))
                    .padding(horizontal = 12.dp, vertical = 5.dp)
                    .noRippleClickable {
                        onClick()
                    }
                    .constrainAs(refItemBtn) {
                        end.linkTo(parent.end, 12.dp)
                        top.linkTo(refIcon.top, 7.dp)
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = btnTxt ?: "",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF5e5e5e),
                    ),
                )
            }
        } else {
            Box(
                modifier = Modifier
                    .background(color = Color(0x334F90FF), shape = RoundedCornerShape(16.dp))
                    .padding(horizontal = 12.dp, vertical = 5.dp)
                    .noRippleClickable {
                        onClick()
                    }
                    .constrainAs(refItemBtn) {
                        end.linkTo(parent.end, 12.dp)
                        top.linkTo(refIcon.top, 7.dp)
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = btnTxt ?: "",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF005EFF),
                    ),
                )
            }
        }


    }
}

@Preview
@Composable
private fun PreviewCertCard() {
    CertCard(
        title = "新手任务",
        showRightMore = true,
        url = "",
        subTitle = "title",
        subContent = "content",
        btnTxt = "查看",
        btnStyle = 1,
        onMoreClick = {},
        onClick = {}
    )
}

@Preview
@Composable
private fun PreviewCertCard2() {
    CertCard(
        title = "新手任务",
        showRightMore = true,
        url = "",
        subTitle = "title",
        subContent = "content",
        btnTxt = "查看",
        btnStyle = 2,
        onMoreClick = {},
        onClick = {}
    )
}

fun clickEditInfo(userTabModel: UserTabModel?, context: Context) {
    if (userTabModel == null) return;
    if (userTabModel.blockInfo.block) {
        if (context is FragmentActivity) {
            MePageRouter.showMeBlockInfoWithInfo(
                context,
                userTabModel.blockInfo,
                PageSource.USER_EDIT_INFO_ACTIVITY,
                "",
                AccountHelper.getInstance().getUserInfo()?.userId ?: ""
            )
        }
    } else {
        if (userTabModel.isNewPage) {
            MePageRouter.jumpToPersonEditActivity(context, "", userTabModel.index);
        } else {
            MePageRouter.jumpToMeInfoEditActivity(context)
        }
    }
}


@Composable
fun MeetingPlanTabMyBottomCard(
    modifier: Modifier = Modifier,
    viewModel: MeComposeViewModel = viewModel(),
    inPreview: Boolean = false,
) {
    val context = LocalContext.current
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(color = Color(0xFFFFFFFF))
            .padding(vertical = 16.dp)
    ) {
        val userTabModel = viewModel.userTabModel
        val menuList = viewModel.userTabModel?.menuList ?: emptyList<MenuBean>()
        val officeActivityBean = viewModel.officeActivityBean

        Column {
            menuList.forEach { menuItem ->
                MeetingPlanTabMyItem(
                    url = menuItem.menuIcon,
                    title = menuItem.menuTitle,
                    tipContent = menuItem.tipContent,
                    showRedPoint = false,
                    onClick = {
                        if (menuItem.jumpUrl.isNotEmpty()) {
                            ProtocolHelper.parseProtocol(menuItem.jumpUrl)
                        }
                    })

            }

            MeetingPlanTabMyItem(
                title = "我的二维码",
                id = R.drawable.me_ic_icon_my_qr_code,
                showRedPoint = false,
                onClick = {
                    if ((userTabModel?.blockInfo?.block == true && userTabModel.blockInfo.baseInfoBlock == true)
                        || !AccountHelper.getInstance().isCanMatch()
                    ) {
                        T.ss("请先完成新手任务哦");
                    } else {
                        MePageRouter.jumpQRCodeActivity(
                            context,
                            PageSource.F4_ME_CHILD_FRAGMENT,
                            "0"
                        )
                    }
                })

            MeetingPlanTabMyItem(
                title = "我的认证",
                tipContent = userTabModel?.certInfo?.tipContent,
                id = R.drawable.me_ic_icon_verity,
                showRedPoint = false,
                onClick = {
                    if (userTabModel?.blockInfo?.block == true && userTabModel.blockInfo.baseInfoBlock == true) {
                        T.ss("请先完成个人信息填写");
                    } else {
                        MePageRouter.jumpToMeAuthActivity(
                            context,
                            PageSource.F4_ME_CHILD_FRAGMENT,
                            ""
                        )
                    }
                })

            val str = if (userTabModel?.moment?.publishStatus == 1) "" else "去发布"
            MeetingPlanTabMyItem(
                title = "我的动态",
                id = R.drawable.me_ic_icon_friend_group,
                tipContent = str,
                showRedPoint = userTabModel?.moment?.showRedPoint == 1,
                onClick = {
                    if (userTabModel == null) return@MeetingPlanTabMyItem
                    if (userTabModel.showNoviceTask == 1) {
                        T.ss("请先完成新手任务哦")
                        return@MeetingPlanTabMyItem
                    }
                    if (AccountHelper.getInstance().isUserCommunityLocked()) {
                        T.ss("社区功能被限制")
                        return@MeetingPlanTabMyItem
                    }
                    if (userTabModel.moment.showRedPoint == 1) {
                        URLConfig.URL_MINE_RED_POINT_CLEAR.simplePost()
                    }
                    if (userTabModel.moment.publishStatus == 0) {
                        SocialPageRouter.jumpToPublishActivity(
                            context,
                            "",
                            Constants.PUBLISH_TYPE_SQUARE
                        )
                    } else {
                        SocialPageRouter.jumpToUserDynamicActivity(
                            context,
                            AccountHelper.getInstance().getUserId()
                        )
                    }
                })

            MeetingPlanTabMyItem(
                title = "帮助与反馈",
                id = R.drawable.me_ic_icon_setting_help,
                showRedPoint = false,
                onClick = {
                    MePageRouter.jumpToSettingHelpActivity(context)
                })

            MeetingPlanTabMyItem(
                title = "设置",
                id = R.drawable.me_ic_icon_me_setting,
                showRedPoint = false,
                onClick = {
                    AppUtil.startUri(context, MePageRouter.ME_SETTING)
                })
        }

    }
}

@Composable
fun MeetingPlanTabMyItem(
    url: String? = "",
    @DrawableRes id: Int = R.drawable.me_ic_icon_my_qr_code,
    title: String? = "",
    tipContent: String? = "",
    showRedPoint: Boolean = false,
    onClick: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .background(color = Color(0xFFFFFFFF))
            .padding(horizontal = 16.dp, vertical = 16.dp)
            .fillMaxWidth()
            .noRippleClickable {
                onClick()
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (url?.isNotEmpty() == true) {
            AsyncImage(
                model = url,
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.size(20.dp)
            )
        } else {
            Image(
                painter = painterResource(id = id),
                contentDescription = "image description",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.size(20.dp)
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = title ?: "",
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF292929),
            ),
            maxLines = 1,
            overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        )

        Text(
            text = tipContent ?: "",
            style = TextStyle(
                fontSize = 13.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFFB8B8B8),
            )
        )
        Spacer(modifier = Modifier.width(4.dp))

        if (showRedPoint) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = R.color.image_color_red.colorResource(),
                        shape = CircleShape
                    )
            )

            Spacer(modifier = Modifier.width(4.dp))
        }


        Image(
            painter = painterResource(id = R.drawable.me_ic_gray_right_arrow),
            contentDescription = "image description",
            contentScale = ContentScale.None,
            modifier = Modifier
        )

    }
}


@Preview
@Composable
private fun PreviewMeetingPlanTabMyItem() {
    MeetingPlanTabMyItem(
        url = "",
        title = "我的活动",
        tipContent = "tipContent",
        showRedPoint = true
    )
}


@Preview
@Composable
private fun PreviewMeetingPlanTabMyBottomCard() {
    MeetingPlanTabMyBottomCard(
    )
}


