package com.kanzhun.marry.me.identify

import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.http.response.BaseResponse
import com.kanzhun.marry.me.api.model.CompanyCertTypeListModel
import com.kanzhun.marry.me.api.model.UserCertListModelK
import io.reactivex.rxjava3.core.Observable
import retrofit2.http.GET
import retrofit2.http.Query

interface API {
    /**
     * 查询当前用户个人认证情况
     */
    @GET(URLConfig.URL_USER_CERT_LIST)
    suspend fun queryUserCertList(@Query("source") source:String): BaseResponse<UserCertListModelK>?

    @GET(URLConfig.URL_ORANGE_USER_GET_UPGRADE_CERT)
    fun getUpgradeCert(): Observable<BaseResponse<UserCertListModelK>>

    @GET(URLConfig.URL_CERT_COMPANY_CERT_TYPE_LIST)
    fun getCompanyCertTypeList(): Observable<BaseResponse<CompanyCertTypeListModel>>

}