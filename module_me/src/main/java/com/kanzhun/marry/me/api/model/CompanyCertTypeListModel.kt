package com.kanzhun.marry.me.api.model

import java.io.Serializable

/**
 * Response model for company certification type list
 * API: orange/cert/company/certTypeList
 */
data class CompanyCertTypeListModel(
    val rows: ArrayList<Int>? = null
) : Serializable {
    companion object {
        const val TYPE_SOCIAL_SECURITY = 1 // 社保
        const val TYPE_WORK_CARD = 2 // 工牌
        const val TYPE_EMAIL = 3 // 邮箱
        const val TYPE_ENTERPRISE_CHAT = 4 // 企业微信/钉钉
        const val TYPE_PERSONAL_TAX = 5 // 个人所得税
    }
}
