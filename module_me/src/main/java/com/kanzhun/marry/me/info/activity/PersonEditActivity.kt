package com.kanzhun.marry.me.info.activity

import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.common.kotlin.ui.statusbar.useBlackTextStatusBar
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.lib_share.ShareAction
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityPersonEditBinding
import com.kanzhun.marry.me.info.fragment.PersonEditFragment
import com.kanzhun.marry.me.info.fragment.PreviewMeFragment
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel
import com.kanzhun.marry.me.point.MePointReporter
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.sankuai.waimai.router.annotation.RouterUri
import com.techwolf.lib.tlog.TLog

//新个人编辑页
@RouterUri(path = [MePageRouter.PERSON_EDIT_ACTIVITY])
class PersonEditActivity : BaseBindingActivity<MeActivityPersonEditBinding, MyInfoEditViewModel>() {
    companion object {
        const val PAGE_EDIT = 0
        const val PAGE_PREVIEW = 1
    }
    lateinit var adapter:ViewPagerFragmentAdapter
    override var setStatusBar = {
        useBlackTextStatusBar(R.color.common_color_F5F5F5.toResourceColor())
    }

    override fun preInit(intent: Intent) {
        fullScreenAndBlackText(marginView = mBinding.idAppTitleView, marginFalsePaddingTrue = true)
//        mBinding.fragmentContainer.addStatusPadding()
    }
    val list: MutableList<Fragment> = mutableListOf()
    override fun initView() {

        mBinding.apply {
            idHideImage.onClick {
                idHideImage.gone()
                SpManager.putUserBoolean("PersonEditActivity_showhide",false)
            }
            idAppTitleView.asBackButton()
            adapter = ViewPagerFragmentAdapter(this@PersonEditActivity)
            idViewPager2.adapter = adapter
            idViewPager2.offscreenPageLimit = 1


            list.add(PersonEditFragment().apply {
                if(intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1)?.isNotEmpty() == true){
                    val bundle = Bundle()
                    bundle.putString(BundleConstants.BUNDLE_DATA_STRING_1, intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1))
                    arguments = bundle
                }

            })
            list.add(PreviewMeFragment().apply {
                val bundle = Bundle()
                bundle.putBoolean("useBar", false)
                arguments = bundle
            })
            adapter.setNewData(list as List<Fragment>?)
            idAppTitleView.setTabStyle("资料编辑","主页预览",{
                idViewPager2.setCurrentItem(0,true)

            },{
                idViewPager2.setCurrentItem(1,true)
                reportPoint("myinfo-preview-click")
            })
            idViewPager2.post {
                idViewPager2.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                    override fun onPageSelected(position: Int) {
                        super.onPageSelected(position)
                        if(position == PAGE_PREVIEW){
                            barChangeItem(lastScrollY)
                            if (list.get(position) is PreviewMeFragment){
                                (list.get(position) as PreviewMeFragment).onRetry()
                            }
                        }else{
                            mBinding.idAppTitleView.setBackgroundColor(R.color.common_translate.toResourceColor())
                            mBinding.idAppTitleView.binding.ivCommonRight.gone()
                            idAppTitleView.setTabSelect(position,false)
                        }
                    }

                    override fun onPageScrollStateChanged(state: Int) {
                        super.onPageScrollStateChanged(state)
                    }

                    override fun onPageScrolled(
                        position: Int,
                        positionOffset: Float,
                        positionOffsetPixels: Int
                    ) {
                        super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                    }
                })
            }

        }

        liveEventBusObserve("PersonEditActivity_check") { it: Boolean ->
            checkHide()
        }

    }

    override fun onBackPressed() {
        var hasStoryListEditFragmentNew = false
        supportFragmentManager.fragments.mapIndexed { _, fragment ->
            if ("StoryListEditFragmentNew" == fragment.tag) {
                hasStoryListEditFragmentNew = true
            }
        }
        if(!hasStoryListEditFragmentNew && list.size > 0){
            val fragment = list.get(0)
            if (fragment is PersonEditFragment){
                if (fragment.adapterClass.isUploading() || fragment.mViewModel.isStoryUploading()) {
                    showTwoButtonDialog(
                        "有照片未完成上传",
                        content = "现在退出未上传的照片将无法保存",
                        cancelable = true,
                        canceledOnTouchOutside = true,
                        negativeText = "继续上传",
                        negativeButtonClick = {

                        },
                        positiveText = "退出",
                        positiveButtonClick = {
                            fragment.adapterClass.disposeRequest()
                            fragment.mViewModel.disposeStoryRequest()
                            AppUtil.finishActivity(this)
                        })
                } else {
                    super.onBackPressed()
                }
            } else {
                super.onBackPressed()
            }
        }else{
            super.onBackPressed()
        }
    }

    private var lastScrollY = 0
    private var scrollY = 0
    var h :Int = 0
    var color:Int = 0
    override fun initData() {
        val targetIndex = intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT_1, PAGE_EDIT)
        if (targetIndex > PAGE_EDIT) {
            switchPage(position = targetIndex)
            mBinding.idAppTitleView.setTabSelect(targetIndex)
        }
        h = QMUIDisplayHelper.getScreenWidth(this)
        color = ContextCompat.getColor(this, R.color.color_white) and 0x00ffffff
        liveEventBusObserve<Int>("ScrollScaleTopBgPerformance-scrollY"){
            if(mBinding.idViewPager2.currentItem == 1){
                barChange(it)
            }
        }
    }

    private fun barChange(it: Int) {
        var mScrollY = it
        TLog.print("scrollY1", lastScrollY.toString())
        TLog.print("scrollY2", mScrollY.toString())
        TLog.print("scrollY h", h.toString())
        if (lastScrollY < h) {
            mScrollY = barChangeItem(mScrollY)
        }
        lastScrollY = mScrollY
    }

    private fun barChangeItem(mScrollY: Int): Int {
        var mScrollY1 = mScrollY
        scrollY = Math.min(h, mScrollY1)
        mScrollY1 = Math.min(mScrollY1, h)
        val alpha = 1f * mScrollY1 / h
        //                mBinding.idAppTitleView.alpha = alpha
        //                mBinding.idAppTitleView.setBackgroundColor(255 * mScrollY / h shl 24 or color)
        if (alpha > 0.5) {
            //                    mBinding.tvTitle.visible()
            //                    mBinding.ivBack.setImageResource(R.mipmap.common_ic_black_preview_back)
            //                    mBinding.ivShare.setImageResource(R.mipmap.common_icon_user_info_share_preview_black)
            mBinding.idAppTitleView.setBackgroundColor(R.color.color_white.toResourceColor())
            mBinding.idAppTitleView.setTabSelect(1, false)
            mBinding.idAppTitleView.setRightIcon(R.mipmap.common_icon_user_info_share_preview_black){
                MePointReporter.reportUserDetailShare(
                    AccountHelper.getInstance().userId
                )
                ShareAction(this).share(AccountHelper.getInstance().userId, "1")
            }
            //                    mBinding.idAppTitleView.binding.ivCommonBack.setImageResource(R.mipmap.common_ic_white_back_2)
            //                    mBinding.idAppTitleView.binding.ivCommonRight.setImageResource(R.mipmap.common_icon_user_info_share_white_2)
        } else {
            mBinding.idAppTitleView.setTabSelect(1, true)
            mBinding.idAppTitleView.setBackgroundColor(R.color.common_translate.toResourceColor())
            mBinding.idAppTitleView.setRightIcon(R.mipmap.common_icon_user_info_share_preview_white){
                MePointReporter.reportUserDetailShare(
                    AccountHelper.getInstance().userId
                )
                ShareAction(this).share(AccountHelper.getInstance().userId, "1")
            }
            //                    mBinding.tvTitle.gone()
            //                    mBinding.idAppTitleView.binding.ivCommonBack.setImageResource(R.mipmap.common_ic_white_back_2)
            //                    mBinding.idAppTitleView.binding.ivCommonRight.setImageResource(R.mipmap.common_icon_user_info_share_white_2)
        }
        return mScrollY1
    }

    fun switchPage(position: Int) {
        mBinding.idViewPager2.currentItem = position

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if(intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1)?.isNotEmpty() == true){
            LiveEventBus.post(LivedataKeyMe.PERSON_EDIT_SCROLL_TO, intent.getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1))
        }
    }

    override fun onRetry() {

    }

    override fun getStateLayout() = null

    @Suppress("OVERRIDE_DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK) {
            return
        }
        when (requestCode) {
            RequestCodeConstants.REQUEST_CODE_ME_UPLOAD_AVATAR,//头像
            RequestCodeConstants.REQUEST_CODE_ME_UPDATE_NICK_NAME,//昵称
            RequestCodeConstants.REQUEST_CODE_ME_AB_FACE_IMPRESSION,//AB
            RequestCodeConstants.REQUEST_CODE_ME_ANSWER,//问答
            RequestCodeConstants.REQUEST_CODE_STORY,//图片
            RequestCodeConstants.REQUEST_CODE_SELECT_LABEL,//标签
            RequestCodeConstants.REQUEST_CODE_LOVE_TEST,//恋爱测试
            RequestCodeConstants.REQUEST_CODE_MARRY_STATUS,//婚恋状态
            RequestCodeConstants.REQUEST_CODE_BASE_INFO,//基本信息
            RequestCodeConstants.REQUEST_CODE_SINGLE_REASON,//邀请其他人绑定
            RequestCodeConstants.REQUEST_CODE_WEB,
                -> {
                LiveEventBus.post(LivedataKeyMe.PERSON_EDIT_REFRESH, true)
            }
        }
    }

    private fun checkHide() {
        if (SpManager.get().user().getBoolean("PersonEditActivity_showhide", true)) {
            mBinding.idHideImage.visible()
        }
    }
}