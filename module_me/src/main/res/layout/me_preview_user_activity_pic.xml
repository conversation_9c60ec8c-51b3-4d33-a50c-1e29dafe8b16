<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginTop="12dp"
    android:background="@color/common_white"
    android:orientation="vertical"
    app:qmui_radius="12dp"
    tools:ignore="SpUsage"
    tools:targetApi="p">

    <LinearLayout
        android:id="@+id/idTop"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvAboutMe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="看准活动照片"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_20"
            />

        <ImageView
            android:layout_marginStart="8dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/image_me_preview_user_activity_pic_icon"
            android:layout_width="60dp"
            android:layout_height="22dp"/>

    </LinearLayout>


    <com.youth.banner.Banner
        android:id="@+id/idRecyclerView"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/idTop"
        android:layout_width="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        android:orientation="horizontal"
        app:banner_auto_loop="true"
        app:banner_infinite_loop="true"
        app:banner_loop_time="3000"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintDimensionRatio="h,351:468"
        tools:listitem="@layout/me_preview_user_activity_pic_item"
        android:layout_height="0dp"/>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>