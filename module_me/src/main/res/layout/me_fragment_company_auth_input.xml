<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/common_white"
        android:orientation="vertical">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/idTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_bottom_padding="@dimen/app_layout_page_content_margin"
            app:title_icon="@drawable/me_ic_company_page"
            app:title_text="你就职的公司或单位是？" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:visibility="gone"
                android:id="@+id/tv_content_total_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:text="/46"
                android:textColor="@color/common_color_B2B2B2"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constraintBottom_toBottomOf="@+id/edit_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/edit_text" />

            <TextView
                android:visibility="gone"
                android:id="@+id/tv_content_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constraintBottom_toBottomOf="@+id/edit_text"
                app:layout_constraintRight_toLeftOf="@+id/tv_content_total_count"
                app:layout_constraintTop_toTopOf="@+id/edit_text"
                tools:text="0" />

            <EditText
                android:id="@+id/edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="10dp"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:background="@null"
                android:gravity="start"
                android:hint="@string/me_company_auth_input_hint"
                android:textColor="@color/common_color_191919"
                android:textColorHint="@color/common_color_B2B2B2"
                android:textSize="@dimen/common_text_sp_18"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/view_line"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_content_count"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="12344556dfgdsdffjisdsdfofsdsj" />


            <View
                android:id="@+id/view_line"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="20dp"
                android:background="@color/common_color_EBEBEB"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/edit_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview"
            app:layout_constraintTop_toBottomOf="@+id/cl_input"
            app:layout_constraintBottom_toTopOf="@+id/btnNext"
            android:layout_marginBottom="15dp"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:layout_width="match_parent"
            android:layout_height="0dp"
             />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btnNext"
            style="@style/button_large_next_page_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.CompanyAuthInputCallback" />

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.CompanyAuthViewModel" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.CompanyAuthInputViewModel" />
    </data>
</layout>