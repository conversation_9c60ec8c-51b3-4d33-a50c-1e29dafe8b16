<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="name"
            type="String" />

        <variable
            name="showAudit"
            type="Boolean" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:src="@drawable/common_bg_circle_fd6666_50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{name}"
            app:layout_constraintHorizontal_chainStyle="spread"
            android:textColor="@color/common_color_0D0D1D"
            android:textSize="@dimen/common_text_sp_16"
            android:layout_marginStart="9dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="学历认证学历认证" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>