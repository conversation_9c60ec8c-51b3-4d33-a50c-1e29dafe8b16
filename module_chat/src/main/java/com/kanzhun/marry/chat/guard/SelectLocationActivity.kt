package com.kanzhun.marry.chat.guard

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetScaffold
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberBottomSheetScaffoldState
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.MapView
import com.amap.api.maps.model.CameraPosition
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.MyLocationStyle
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.photo.zoomable.previewer.PreviewerState
import com.kanzhun.common.photo.zoomable.previewer.rememberPreviewerState
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.LText
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.guard.ui.PreviewImage
import com.kanzhun.marry.chat.guard.ui.SelectLocationBottomSheet
import com.kanzhun.marry.chat.guard.ui.buildAddressImageKey
import com.kanzhun.marry.chat.guard.viewmodel.SelectLocationViewModel
import com.kanzhun.marry.location.GPSTracker
import com.kanzhun.marry.location.GPSTracker.Companion.setLastLocation
import com.kanzhun.utils.T
import kotlinx.coroutines.launch

/**
 * Activity for selecting a location on the map
 */
class SelectLocationActivity : BaseComposeActivity() {
    @Suppress("unused")
    companion object {
        private const val EXTRA_SELECTED_POI = "extra_selected_poi"
        private const val EXTRA_LATITUDE = "extra_latitude"
        private const val EXTRA_LONGITUDE = "extra_longitude"
        private const val EXTRA_ADDRESS_NAME = "extra_address_name"
        private const val EXTRA_ADDRESS = "extra_address"
        private const val EXTRA_PROVINCE = "extra_province"
        private const val EXTRA_CITY = "extra_city"
        private const val EXTRA_DISTRICT = "extra_district"
        private const val EXTRA_IS_RECOMMENDED = "extra_is_recommended"
        private const val EXTRA_ID = "extra_id"

        // Beijing default location: 39.908714,116.3870893
        private const val DEFAULT_LATITUDE = 39.908714
        private const val DEFAULT_LONGITUDE = 116.3870893

        // 用于传递已选地址的信息
        const val EXTRA_SELECTED_LATITUDE = "extra_selected_latitude"
        const val EXTRA_SELECTED_LONGITUDE = "extra_selected_longitude"
        const val EXTRA_SELECTED_ADDRESS = "extra_selected_address"
        const val EXTRA_SELECTED_ID = "extra_selected_id"
        const val EXTRA_PEER_ID = "extra_peer_id"

        /**
         * Start the activity
         * @param context Context
         * @param selectedLatitude 已选地址的纬度，如果有的话
         * @param selectedLongitude 已选地址的经度，如果有的话
         * @param selectedAddress 已选地址的名称，如果有的话
         * @param selectedId 已选地址的ID，用于匹配和显示勾选状态
         */
        fun start(
            context: Context,
            selectedLatitude: Double? = null,
            selectedLongitude: Double? = null,
            selectedAddress: String? = null,
            selectedId: String? = null
        ) {
            val intent = Intent(context, SelectLocationActivity::class.java).apply {
                selectedLatitude?.let { putExtra(EXTRA_SELECTED_LATITUDE, it) }
                selectedLongitude?.let { putExtra(EXTRA_SELECTED_LONGITUDE, it) }
                selectedAddress?.let { putExtra(EXTRA_SELECTED_ADDRESS, it) }
                selectedId?.let { putExtra(EXTRA_SELECTED_ID, it) }
            }
            context.startActivity(intent)
        }

        /**
         * Get the result from the intent
         */
        fun getLocationResult(data: Intent?): LocationResult? {
            if (data == null) return null

            return LocationResult(
                latitude = data.getDoubleExtra(EXTRA_LATITUDE, 0.0),
                longitude = data.getDoubleExtra(EXTRA_LONGITUDE, 0.0),
                addressName = data.getStringExtra(EXTRA_ADDRESS_NAME) ?: "",
                address = data.getStringExtra(EXTRA_ADDRESS) ?: "",
                province = data.getStringExtra(EXTRA_PROVINCE) ?: "",
                city = data.getStringExtra(EXTRA_CITY) ?: "",
                district = data.getStringExtra(EXTRA_DISTRICT) ?: "",
                isRecommendedAddress = data.getBooleanExtra(EXTRA_IS_RECOMMENDED, false),
                id = data.getStringExtra(EXTRA_ID) ?: ""
            )
        }
    }

    // Store the MapView reference
    private var myLocation: LatLng? = null

    private var mapView: MapView? = null

    private lateinit var gpsTracker: GPSTracker

    // Flag to track if user has manually moved the map
    private var hasMoveToMyLocation = false

    override fun enableSafeDrawingPadding() = false

    // 已选地址的信息
    private var selectedLatitude: Double? = null
    private var selectedLongitude: Double? = null
    private var selectedAddress: String? = null
    private var selectedId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }

        // 从 Intent 中获取已选地址的信息
        selectedLatitude =
            intent.getDoubleExtra(EXTRA_SELECTED_LATITUDE, Double.NaN).takeIf { !it.isNaN() }
        selectedLongitude =
            intent.getDoubleExtra(EXTRA_SELECTED_LONGITUDE, Double.NaN).takeIf { !it.isNaN() }
        selectedAddress = intent.getStringExtra(EXTRA_SELECTED_ADDRESS)
        selectedId = intent.getStringExtra(EXTRA_SELECTED_ID)

        gpsTracker = GPSTracker(this)

        val hasLocationPermission = gpsTracker.hasLocationPermission(this)
        if (!hasLocationPermission) {
            requestMyLocation()

            reportPoint("O2-seeyou-positionauthorwin-show")
        }

        super.onCreate(savedInstanceState)

        reportPoint("O2-seeyou-positionsearch-show") {
            peer_id = intent.getStringExtra(EXTRA_PEER_ID) ?: ""
        }
    }

    @Composable
    override fun OnSetContent() {
        Content()
    }

    @Composable
    @OptIn(ExperimentalMaterial3Api::class)
    private fun Content(inPreview: Boolean = false) {
        val viewModel: SelectLocationViewModel = viewModel()

        // 设置初始选中的地址
        LaunchedEffect(Unit) {
            viewModel.setInitialSelectedLocation(
                latitude = selectedLatitude,
                longitude = selectedLongitude,
                address = selectedAddress,
                id = selectedId
            )
        }

        val currentMapLocation by viewModel.currentMapLocation.collectAsState()
        val selectedLocation by viewModel.selectedLocation.collectAsState()

        val addressModel by viewModel.addressModel.collectAsState()
        val nearbyPois by viewModel.nearbyPois.collectAsState()
        val searchResults by viewModel.searchResults.collectAsState()
        val isLoading by viewModel.isLoading.collectAsState()
        val searchQuery by viewModel.searchQuery.collectAsState()
        val isSearchMode by viewModel.isSearchMode.collectAsState()

        // Screen height in dp
        val screenHeight = LocalConfiguration.current.screenHeightDp.dp
        val density = LocalDensity.current

        // Map height fractions
        val mapCollapsedHeightFraction = 0.22f  // 22% of screen height when sheet is expanded
        val mapExpandedHeightFraction = 0.40f   // 40% of screen height when sheet is collapsed

        // Create bottom sheet state with two stable states
        val scope = rememberCoroutineScope()
        val sheetState = rememberStandardBottomSheetState(
            initialValue = SheetValue.PartiallyExpanded,
            skipHiddenState = true,  // Don't allow hidden state
            confirmValueChange = { sheetValue ->
                // Allow transitions between PartiallyExpanded and Expanded
                when (sheetValue) {
                    SheetValue.PartiallyExpanded, SheetValue.Expanded -> true
                    else -> false
                }
            }
        )
        val scaffoldState = rememberBottomSheetScaffoldState(bottomSheetState = sheetState)

        // Calculate sheet peek heights based on map height fractions
        val expandedSheetHeight =
            screenHeight * (1 - mapCollapsedHeightFraction)  // 78% of screen height
        val partiallyExpandedSheetHeight =
            screenHeight * (1 - mapExpandedHeightFraction)  // 60% of screen height

        // Track the bottom sheet position
        var bottomSheetBounds by remember {
            mutableStateOf<Rect>(
                Rect(0f, 0f, 0f, 0f)
            )
        }

        val recommendPics = addressModel?.recommendPics ?: emptyList()
        val addressKey = SelectLocationActivity::class.java
        var previewerState: PreviewerState = rememberPreviewerState(
            key1 = addressModel,
            pageCount = { recommendPics.size },
            getKey = {
                buildAddressImageKey(
                    index = it,
                    url = recommendPics.getOrNull(it).toString(),
                    key1 = addressKey
                )
            }
        )
        if (previewerState.canClose || previewerState.animating) {
            BackHandler {
                if (previewerState.canClose) {
                    scope.launch {
                        previewerState.exitTransform()
                    }
                }
            }
        }

        // 是否是程序交互地图
        var isMapInteractProgrammatically by remember { mutableStateOf(false) }

        LaunchedEffect(selectedLocation) {
            selectedLocation?.let {
                // Move map to selected location
                mapView?.map?.moveCamera(
                    CameraUpdateFactory.newLatLngZoom(
                        LatLng(LText.getDouble(it.latitude), LText.getDouble(it.longitude)),
                        15f
                    )
                )
            }
        }

        // Main content with map and bottom sheet
        BottomSheetScaffold(
            scaffoldState = scaffoldState,
            sheetContent = {
                SelectLocationBottomSheet(
                    myLocation = myLocation,
                    searchQuery = searchQuery,
                    isSearchMode = isSearchMode,
                    isLoading = isLoading,
                    addressModel = addressModel,
                    nearbyPois = nearbyPois,
                    searchResults = searchResults,
                    selectedLocation = selectedLocation,
                    onSearchQueryChange = { viewModel.updateSearchQuery(it, myLocation) },
                    onSearch = { viewModel.performSearch() },
                    onClearSearch = { viewModel.clearSearch() },
                    onLocationSelected = {
                        // 代码自动拖动地图
                        isMapInteractProgrammatically = true

                        // Select the location
                        viewModel.selectLocation(it)
                    },
                    onSearchFocused = {
                        // 当搜索框获取焦点时，将 BottomSheet 完全展开
                        scope.launch {
                            sheetState.expand() // 将状态设置为 Expanded
                        }
                    },
                    addressKey = addressKey,
                    previewerState = previewerState,
                    onPreview = { index ->
                        scope.launch {
                            previewerState.enterTransform(index = index)
                        }
                    },
                    modifier = Modifier
                        .heightIn(max = expandedSheetHeight)
                        .onGloballyPositioned { coordinates: LayoutCoordinates ->
                            // Get the position of the bottom sheet in window coordinates
                            bottomSheetBounds = coordinates.boundsInWindow()
                        }
                )
            },
            sheetPeekHeight = partiallyExpandedSheetHeight,
            sheetSwipeEnabled = true,
            sheetShape = RoundedCornerShape(topStart = 32.dp, topEnd = 32.dp),
            sheetContainerColor = Color.Transparent,
            contentColor = Color.Transparent,
            containerColor = Color.Transparent,
            sheetContentColor = Color.Transparent,
            sheetDragHandle = null,
            modifier = Modifier.fillMaxSize()
        ) { paddingValues ->
            // Map container - takes up the available space above the bottom sheet

            // Calculate map height based on sheet position

            // Then use the real-time position
            val screenHeightDp = LocalConfiguration.current.screenHeightDp
            val sheetHeightFromPosition by remember {
                derivedStateOf {
                    with(density) {
                        // Convert from pixels to dp
                        // 屏幕高度减去底部表单的顶部位置，得到底部表单的高度
                        val screenHeightPx = screenHeightDp * density.density
                        (screenHeightPx - bottomSheetBounds.top).toDp()
                    }
                }
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = sheetHeightFromPosition)
                    .conditional(inPreview) {
                        background(color = Color.LightGray)
                    }
            ) {
                // Map container
                AndroidView(
                    factory = { context ->
                        MapView(context).apply {
                            // Store the MapView reference in the activity
                            <EMAIL> = this
                            onCreate(Bundle())

                            // Set up map settings
                            map?.uiSettings?.apply {
                                isZoomControlsEnabled = false
                                isCompassEnabled = false
                                isMyLocationButtonEnabled = false
                                isScaleControlsEnabled = false
                                isRotateGesturesEnabled = false
                                isTiltGesturesEnabled = false
                            }

                            // Enable my location
                            map?.isMyLocationEnabled = true
                            map?.myLocationStyle = MyLocationStyle().apply {
                                myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER)
                                interval(1000)
                            }

                            // Set map type
                            map?.mapType = AMap.MAP_TYPE_NORMAL

                            // Listen for camera idle to update location
                            map?.setOnCameraChangeListener(object :
                                AMap.OnCameraChangeListener {
                                override fun onCameraChange(position: CameraPosition?) {
                                    // Not used
                                }

                                override fun onCameraChangeFinish(position: CameraPosition?) {
                                    position?.target?.let { latLng ->
                                        viewModel.updateMapLocation(
                                            latLng = latLng,
                                            isMapInteractProgrammatically = isMapInteractProgrammatically,
                                            myLocation = myLocation
                                        )
                                    }

                                    isMapInteractProgrammatically = false
                                }
                            })

                            map?.setOnMapTouchListener { motionEvent -> }

                            // Initial location listener setup
                            // This will be re-registered with proper behavior after permissions are granted
                            map?.setOnMyLocationChangeListener { location ->
                                location?.let {
                                    val latitude = it.latitude
                                    val longitude = it.longitude
                                    val latLng = LatLng(latitude, longitude)

                                    if (isValidLocation(latitude, longitude)) {
                                        myLocation = latLng // 记录我当前的定位位置
                                        setLastLocation(Location(null).apply {
                                            this.latitude = latitude
                                            this.longitude = longitude
                                        })
                                    }

                                    if (currentMapLocation == null && !hasMoveToMyLocation) {
                                        // Only move to my location when first loaded and user hasn't manually moved the map
                                        map.moveCamera(
                                            CameraUpdateFactory.newLatLngZoom(
                                                latLng,
                                                15f
                                            )
                                        )
                                        hasMoveToMyLocation = true
                                    }
                                }
                            }

                            map?.setOnMapLoadedListener {
                                // Initialize map with Beijing default location
                                val hasLocationPermission =
                                    gpsTracker.hasLocationPermission(this@SelectLocationActivity)
                                if (!hasLocationPermission) {
                                    map.moveCamera(
                                        CameraUpdateFactory.newLatLngZoom(
                                            LatLng(DEFAULT_LATITUDE, DEFAULT_LONGITUDE),
                                            12f
                                        )
                                    )
                                }
                            }
                        }
                    },
                    modifier = Modifier.fillMaxSize(),
                    update = { view ->
                    }
                )

                Spacer(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(379f / 88)
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFF5F5F5),
                                    Color(0x00F5F5F5)
                                )
                            )
                        )
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp)
                        .statusBarsPadding()
                ) {
                    val actionBack = {
                        reportPoint("O2-seeyou-positionsearch-click") {
                            actionp2 = selectedLocation?.name ?: ""  // 点击地址文本
                            actionp3 = selectedLocation?.let { "${it.longitude},${it.latitude}" }
                                ?: ""  // 点击地址gps
                            actionp4 =
                                myLocation?.let { "${it.longitude},${it.latitude}" } ?: ""  // 用户gps
                            actionp5 = viewModel.searchQuery.value  // 用户当前搜索query
                            actionp6 = "返回"  // 点击位置：【确定】【返回】
                            peer_id = intent.getStringExtra(EXTRA_PEER_ID)  // 用户id
                        }

                        AppUtil.finishActivity(this@SelectLocationActivity)
                    }

                    BackHandler {
                        actionBack()
                    }

                    Image(
                        painter = painterResource(R.mipmap.chat_ic_map_back),
                        modifier = Modifier
                            .size(40.dp)
                            .noRippleClickable { actionBack() },
                        contentDescription = ""
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Text(
                        text = "确定",
                        style = TextStyle(
                            fontSize = 16.sp,
                            lineHeight = 24.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFFFFFFF),
                        ),
                        modifier = Modifier
                            .background(
                                color = Color(0xFF191919),
                                shape = RoundedCornerShape(64.dp)
                            )
                            .padding(horizontal = 24.dp, vertical = 8.dp)
                            .noRippleClickable {
                                // 处理确定按钮点击事件
                                selectedLocation?.let { poi ->
                                    // 判断是否是推荐地址
                                    val isRecommendedAddress = addressModel?.let { model ->
                                        // 判断条件：如果选择的地址的ID与推荐地址的ID相同，则认为是推荐地址
                                        poi.id == model.id
                                    } == true

                                    // 创建返回的 Intent
                                    val resultIntent = Intent().apply {
                                        putExtra(EXTRA_LATITUDE, LText.getDouble(poi.latitude))
                                        putExtra(
                                            EXTRA_LONGITUDE,
                                            LText.getDouble(poi.longitude)
                                        )
                                        putExtra(EXTRA_ADDRESS_NAME, poi.name)
                                        putExtra(EXTRA_ADDRESS, poi.address)

                                        // 解析地址信息，获取省市区
                                        putExtra(EXTRA_PROVINCE, poi.province)
                                        putExtra(
                                            EXTRA_CITY,
                                            if (poi.city.isNullOrEmpty() || poi.city == "[]") {
                                                poi.province
                                            } else {
                                                poi.city
                                            }
                                        )
                                        putExtra(EXTRA_DISTRICT, poi.district)

                                        // 添加是否是推荐地址的标志
                                        putExtra(EXTRA_IS_RECOMMENDED, isRecommendedAddress)

                                        // 添加地址ID
                                        putExtra(EXTRA_ID, poi.id)
                                    }

                                    // 设置结果并关闭活动
                                    setResult(RESULT_OK, resultIntent)
                                    AppUtil.finishActivity(this@SelectLocationActivity)

                                    reportPoint("O2-seeyou-positionsearch-click") {
                                        actionp2 = poi.name  // 点击地址文本
                                        actionp3 = "${poi.longitude},${poi.latitude}"  // 点击地址gps
                                        actionp4 =
                                            myLocation?.let { "${it.longitude},${it.latitude}" }
                                                ?: ""  // 用户gps
                                        actionp5 = viewModel.searchQuery.value  // 用户当前搜索query
                                        actionp6 = "确定"  // 点击位置：【确定】【返回】
                                        peer_id = intent.getStringExtra(EXTRA_PEER_ID)  // 用户id
                                    }
                                } ?: run {
                                    // 如果没有选择地址，提示用户
                                    T.ss("请选择一个地址")
                                }
                            }
                    )
                }

                // Center marker
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(R.mipmap.chat_ic_location_on),
                        contentDescription = "Center marker",
                        modifier = Modifier
                            .padding(bottom = 20.dp)
                            .size(height = 40.dp, width = 33.5.dp)
                    )
                }

                val myLocationIconOffsetY by remember {
                    derivedStateOf {
                        with(density) {
                            (bottomSheetBounds.top).toDp()
                        }
                    }
                }
                Image(
                    painter = painterResource(R.mipmap.chat_ic_my_location),
                    modifier = Modifier
                        .padding(12.dp)
                        .size(40.dp)
                        .align(Alignment.TopEnd)
                        .offset(y = myLocationIconOffsetY - 64.dp)
                        .noRippleClickable {
                            // Reset the flag when user explicitly requests to go to their location
                            hasMoveToMyLocation = false
                            requestMyLocation()
                        },
                    contentDescription = ""
                )
            }
        }

        PreviewImage(
            previewerState = previewerState,
            photos = recommendPics,
            onImagePageChanged = { item: String, idx: Int ->

            },
            onClosePreview = {
                scope.launch {
                    previewerState.exitTransform()
                }
            },
        )

        // Handle lifecycle events for MapView
        DisposableEffect(Unit) {
            onDispose {
                mapView?.onDestroy()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mapView?.onResume()
    }

    override fun onPause() {
        super.onPause()
        mapView?.onPause()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView?.onSaveInstanceState(outState)
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView?.onDestroy()
    }

    @SuppressLint("MissingPermission")
    private fun requestMyLocation() {
        PermissionHelper
            .getSafetyGuardLocationHelper(this)
            .setPermissionCallback { granted, _ ->
                val hasLocationPermission = gpsTracker.hasLocationPermission(this)

                if (hasLocationPermission) {
                    // Reload the map to ensure location listeners are properly registered
                    reloadMapAfterPermissionGranted()
                } else {
                    // 如果没有位置权限，地图已经在初始化时设置为北京默认位置
                }

                reportPoint("O2-seeyou-positionauthorwin-click") {
                    actionp2 = if (granted) "允许" else "不允许"
                }
            }
            .requestPermission()
    }

    /**
     * Reload the map after location permission is granted to ensure
     * location listeners are properly registered
     */
    @SuppressLint("MissingPermission")
    private fun reloadMapAfterPermissionGranted() {
        mapView?.map?.let { map ->
            // Re-enable my location features
            map.isMyLocationEnabled = true
            map.myLocationStyle = MyLocationStyle().apply {
                myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER)
                interval(1000)
            }

            // Re-register the location change listener
            map.setOnMyLocationChangeListener { location ->
                location?.let {
                    val latitude = it.latitude
                    val longitude = it.longitude
                    val latLng = LatLng(latitude, longitude)

                    if (isValidLocation(latitude, longitude)) {
                        myLocation = latLng
                        setLastLocation(Location(null).apply {
                            this.latitude = latitude
                            this.longitude = longitude
                        })

                        // Only move to user location if the user hasn't manually moved the map
                        if (!hasMoveToMyLocation) {
                            moveMapToMyLocation(latitude, longitude)
                            hasMoveToMyLocation = true
                        }
                    }
                }
            }
        }
    }

    private fun isValidLocation(latitude: Double, longitude: Double): Boolean {
        return latitude > 0f && latitude < 180f && longitude > 0f && longitude < 180f
    }

    private fun moveMapToMyLocation(latitude: Double, longitude: Double) {
        mapView?.map?.moveCamera(
            CameraUpdateFactory.newLatLngZoom(
                LatLng(latitude, longitude),
                15f
            )
        )
    }

    @Preview(showBackground = true)
    @Composable
    private fun ContentPreview() {
        Content(inPreview = true)
    }
}

/**
 * Data class for location result
 */
data class LocationResult(
    val latitude: Double,
    val longitude: Double,
    val addressName: String,
    val address: String,
    val province: String,
    val city: String,
    val district: String,
    val isRecommendedAddress: Boolean = false, // 标记是否是推荐地址
    val id: String = "" // 地址的ID，用于匹配和显示勾选状态
)