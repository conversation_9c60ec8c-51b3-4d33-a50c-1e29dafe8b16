package com.kanzhun.imageviewer.video;

import android.content.Context;
import android.graphics.RectF;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.kanzhun.imageviewer.R;
import com.kanzhun.imageviewer.interfaces.OnImageViewerViewCallback;
import com.kanzhun.imageviewer.loader.ContentLoader;

import net.mikaelzero.mojito.loader.OnLongTapCallback;
import net.mikaelzero.mojito.loader.OnTapCallback;
import net.mikaelzero.mojito.loader.OnVideoCallback;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/9/6
 */
public class VideoContentLoaderImpl implements ContentLoader, LifecycleEventObserver {
    private View frameLayout;
    private OVideoView videoView;
    private ImageView ivCover;
    private OnVideoCallback callback;

    public boolean simpleMode = false;

    private boolean isAutoPlay = false;

    public VideoContentLoaderImpl(boolean simpleMode, boolean isAutoPlay) {
        this.simpleMode = simpleMode;
        this.isAutoPlay = isAutoPlay;
    }

    @NonNull
    @Override
    public RectF getDisplayRect() {
        return new RectF();
    }

    @Override
    public void init(@NonNull Context context, @NonNull String originUrl, @Nullable String targetUrl, @Nullable OnImageViewerViewCallback onImageViewerViewCallback) {
        frameLayout = LayoutInflater.from(context).inflate(R.layout.image_video_layout, null);
        videoView = frameLayout.findViewById(R.id.video_view);
        ivCover = videoView.getVideoCover();
//        videoView.setCover();
        ivCover.setScaleType(ImageView.ScaleType.CENTER_CROP);
        videoView.setDataSource(originUrl);
//        videoView.setCover(targetUrl);
        videoView.setSimpleMode(simpleMode);
        videoView.setAutoPlay(isAutoPlay);
        videoView.setCallback(new OVideoView.OVideoViewCallback() {
            @Override
            public void saveFile(String path) {
                if (callback != null) {
                    callback.onSaveFile(path);
                }
            }

            @Override
            public void closePage() {
                if (callback != null) {
                    callback.onClosPage();
                }
            }
        });
    }

    @NonNull
    @Override
    public View providerView() {
        return frameLayout;
    }

    @NonNull
    @Override
    public View providerRealView() {
        return ivCover;
    }

    @Override
    public boolean dispatchTouchEvent(boolean isDrag, boolean isActionUp, boolean isDown, boolean isHorizontal) {
        boolean result;
        if (isDrag && !isHorizontal) {
            result = false;
        } else if (isActionUp) {
            result = !isDrag;
        } else {
            result = isHorizontal;
        }
        return result;
    }

    @Override
    public void dragging(int width, int height, float ratio) {

    }

    @Override
    public void beginBackToMin(boolean isResetSize) {
        if (isResetSize) {
            videoView.beginBackToMin();
        }
    }

    @Override
    public void backToNormal() {
    }

    @Override
    public void loadAnimFinish() {
        ivCover.setScaleType(ImageView.ScaleType.FIT_CENTER);
        videoView.downLoadFile();
    }

    @Override
    public boolean needReBuildSize() {
        return false;
    }

    @Override
    public boolean useTransitionApi() {
        return false;
    }

    @Override
    public boolean isLongImage(int width, int height) {
        return false;
    }

    @Override
    public void onTapCallback(@NonNull OnTapCallback onTapCallback) {

    }

    @Override
    public void onLongTapCallback(@NonNull OnLongTapCallback onLongTapCallback) {

    }

    @Override
    public void pageChange(boolean isHidden) {

    }

    @Override
    public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
        if (videoView != null) {
            videoView.onStateChanged(source, event);
        }
    }

    public void setCallback(OnVideoCallback callback) {
        this.callback = callback;
    }
}
