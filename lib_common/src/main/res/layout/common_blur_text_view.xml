<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flBlurContent"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvBlurText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:visibility="invisible"
        android:maxLines="1"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字模糊文字" />

    <View
        android:id="@+id/iv_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/common_white"
        app:layout_constraintBottom_toBottomOf="@id/tvBlurText"
        app:layout_constraintEnd_toEndOf="@id/tvBlurText"
        app:layout_constraintStart_toStartOf="@id/tvBlurText"
        app:layout_constraintTop_toTopOf="@id/tvBlurText" />


    <ImageView
        android:id="@+id/ivBlur"
        android:visibility="invisible"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvBlurText"
        app:layout_constraintEnd_toEndOf="@id/tvBlurText"
        app:layout_constraintStart_toStartOf="@id/tvBlurText"
        app:layout_constraintTop_toTopOf="@id/tvBlurText" />
</androidx.constraintlayout.widget.ConstraintLayout>