package com.kanzhun.common.kotlin.ktx.viewbinding

import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.viewbinding.ViewBinding

interface FragmentBindingModel<VB : ViewBinding, M : ViewModel> {
    val mBinding: VB
    val mViewModel: M
    fun Fragment.initViewBindingAndViewModel(inflater: LayoutInflater, container: ViewGroup?): View
}

class FragmentBindingModelDelegate<VB : ViewBinding, M : ViewModel>() : FragmentBindingModel<VB, M> {
    private var _binding: VB? = null
    private var _viewModel: M? = null

    private val handler by lazy { Handler(Looper.getMainLooper()) }

    override val mBinding: VB
        get() = requireNotNull(_binding) { "The property of binding has been destroyed." }

    override val mViewModel: M
        get() = requireNotNull(_viewModel) { "The property of viewModel has been destroyed." }

    override fun Fragment.initViewBindingAndViewModel(inflater: LayoutInflater, container: ViewGroup?): View {
        if (_binding == null) {
            _binding = ViewBindingUtil.inflateWithGeneric(this, inflater, container, false)
            viewLifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
                override fun onDestroy(owner: LifecycleOwner) {
                    handler.post { _binding = null }
                }
            })
        }
        _viewModel = ViewModelUtils.createWithGeneric(this, this)
        return _binding!!.root
    }

}