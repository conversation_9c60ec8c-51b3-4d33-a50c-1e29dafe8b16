package com.kanzhun.common.kotlin.ext

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.widget.TextView
import androidx.annotation.ColorInt
import com.kanzhun.common.kotlin.ui.textview.CenterVerticalSpan


/**
 * 由内容，则展示；否则隐藏
 */
fun TextView.textOrGone(content: CharSequence?) {
    text = content
    if (text.isNullOrBlank()) {
        gone()
    } else {
        visible()
    }
}
fun TextView.textOrInvisible(content: CharSequence?) {
    text = content
    if (text.isNullOrBlank()) {
        invisible()
    } else {
        visible()
    }
}

/**
 * 显示数字，如果<0隐藏，>0则显示，>99显示99+
 */
fun TextView.setCountAsText(count: Int) {
    if (count > 0) {
        visible()
        text = count.to99PlusString()
    } else {
        gone()
    }
}


/**
 * 获取一个带分隔符的字符串，类似：男 | 23岁 | 本科 | 9年以上
 * @param context
 * @param stringList
 * @param separator
 * @param sepSize
 * @param sepColor
 * @return
 */
fun TextView.setTextSeparatorSpan(stringList: List<String?>?, separator: String = "|", sepPrefix: String? = "  ",
                                  sepSuffix: String? = "  ", sepSize: Float = 10F, @ColorInt sepColor: Int) {
    if (stringList.isNullOrEmpty()) {
        text = ""
        return
    }
    val sb = StringBuilder()
    val size = stringList.size
    for (i in 0 until size) {
        val str = stringList[i]
        if (!TextUtils.isEmpty(str)) {
            if (sb.isNotEmpty()) {
                sb.append(sepPrefix).append(separator).append(sepSuffix)
            }
            sb.append(str)
        }
    }
    val content = sb.toString()
    val builder = SpannableStringBuilder(content)
    var index = content.indexOf(separator)
    val sepLength = separator.length
    while (index >= 0) {
        val end = index + sepLength
        val sizeSpan = CenterVerticalSpan(sepSize)
        sizeSpan.setColor(sepColor)
        builder.setSpan(sizeSpan, index, end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        index = content.indexOf(separator, end)
    }
    text = content
}

