package com.kanzhun.common.views.textview

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import com.kanzhun.common.util.LText

/**
 * Created by qinsanjie
 * Date: 2023/8/9
 * 嵌入字体库 SourceHanSerifCN-Bold.otf 的textview
 */
class BoldEditText @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) //    private void initTypeface(Context context) {

    : AppCompatEditText(context, attrs, defStyleAttr) {
    init {
        typeface = LText.getBoldTypeface(context);
        includeFontPadding = false
    }


}