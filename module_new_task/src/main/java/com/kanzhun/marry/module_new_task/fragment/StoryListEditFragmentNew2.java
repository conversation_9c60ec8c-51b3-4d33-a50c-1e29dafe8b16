package com.kanzhun.marry.module_new_task.fragment;

import static android.app.Activity.RESULT_OK;
import static com.kanzhun.foundation.views.guideview.DimenUtil.dp2px;

import android.net.Uri;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.widget.ViewPager2;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.common.AvoidOnResult;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.base.FragmentBackHandler;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.bean.BaseStoryEditItem;
import com.kanzhun.foundation.dialog.StoryGuideDialog;
import com.kanzhun.foundation.page.VideoAudioFloatPageManager;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentStoryListEdit3Binding;
import com.kanzhun.marry.me.databinding.MeItemPicStoryBinding;
import com.kanzhun.marry.me.databinding.MeItemVideoStoryCropBinding;
import com.kanzhun.marry.me.info.bean.PicStoryEditItem;
import com.kanzhun.marry.me.info.bean.VideoStoryEditItem;
import com.kanzhun.marry.me.info.callback.StoryListEditCallback;
import com.kanzhun.marry.me.info.fragment.StoryListAddComposableKt;
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel;
import com.kanzhun.marry.me.info.viewmodel.StoryListEditViewModel3;
import com.kanzhun.marry.module_new_task.NewUserTaskActivity;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.file.FileUtils;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;

import org.alita.config.AfantyConfig;
import org.alita.core.AlitaMediaCore;
import org.alita.core.AlitaPlayer;
import org.alita.webrtc.VideoRenderer;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import kotlin.Unit;
import lib.twl.picture.util.Const;

public class StoryListEditFragmentNew2 extends FoundationVMFragment<MeFragmentStoryListEdit3Binding, StoryListEditViewModel3> implements StoryListEditCallback, FragmentBackHandler {
    public static final String TAG = "StoryListEditFragment";

    public static final int TYPE_ADD = 0;//添加新的故事
    public static final int TYPE_EDIT = 1;//对原有的故事重新编辑
    public static final int TYPE__REJECTED_EDIT = 2;//对审核拒绝的故事重新编辑

    private BaseBinderAdapter pagerAdapter;

    private int type;
    private AlitaPlayer player;
    private VideoRenderer mVideoRender;

    private int maxImageCount = 0;

    public void setMaxImageCount(int maxImageCount) {
        this.maxImageCount = maxImageCount;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_story_list_edit3;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getActivity().getWindow().setStatusBarColor(getResources().getColor(com.kanzhun.common.R.color.white));
        if (getArguments().getBoolean("activityViewModel", false) && getActivity() instanceof NewUserTaskActivity) {
            NewUserTaskActivity newUserTaskActivity = (NewUserTaskActivity) getActivity();
            model = newUserTaskActivity.getMViewModel();
        }
        if ( getActivity() instanceof NewUserTaskActivity) {
            setMaxImageCount(getArguments().getInt("maxImageCount", 0));
        }
        getViewModel().init(getArguments().getInt(BundleConstants.BUNDLE_TEXT_REJECT_TYPE, ProfileInfoModel.CONTENT_TYPE_UN_REJECTED),
                getArguments().getInt(BundleConstants.BUNDLE_PHOTO_REJECT_TYPE, ProfileInfoModel.CONTENT_TYPE_UN_REJECTED), getArguments().getString(BundleConstants.BUNDLE_STATURE, ""));
        player = new AlitaPlayer(getContext());
        getDataBinding().svPlayer.init(AlitaMediaCore.getInstance().getEglInstance().getEglBaseContext(), null);
        mVideoRender = new VideoRenderer(getDataBinding().svPlayer);
        player.setRenderer(mVideoRender.GetRenderPointer());
        AfantyConfig config = new AfantyConfig();
        config.enableHardwareDecode = true;
        player.setConfig(config);
        player.setLoop(true);
        getDataBinding().clContent.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        RxBus.getInstance().subscribe(this, TAG, new RxBus.Callback<String>() {

            @Override
            public void onEvent(String s) {
                getViewModel().setCurrentEditContent(s);
                int index = getViewModel().getSelectIndexValue();
                List<BaseStoryEditItem> baseStoryItems = getViewModel().getStoryEditLiveData().getValue();
                if (!LList.isEmpty(baseStoryItems) && index < baseStoryItems.size()) {
                    BaseStoryEditItem baseStoryItem = baseStoryItems.get(index);
                    baseStoryItem.setChangedStoryText(s);
                    if (getViewModel().getOriginTextRejectType() == ProfileInfoModel.CONTENT_TYPE_REJECTED) {
                        getViewModel().getTextRejectType().set(TextUtils.equals(s, baseStoryItem.getStoryText()) ? ProfileInfoModel.CONTENT_TYPE_REJECTED : ProfileInfoModel.CONTENT_TYPE_UN_REJECTED);
                    }
                }
            }
        });
        initPagerAdapter();
        getViewModel().getStoryEditLiveData().observe(this, baseStoryItems -> {
            if (LList.isEmpty(baseStoryItems)) {
                return;
            }
            int selectIndex = getViewModel().getSelectIndexValue();
            pagerAdapter.setList(baseStoryItems);
            getDataBinding().storyContent.setOffscreenPageLimit(baseStoryItems.size());
            getDataBinding().storyContent.setCurrentItem(selectIndex, false);
            getViewModel().setCount(baseStoryItems.size());
        });
        type = getArguments().getInt(BundleConstants.BUNDLE_TYPE);
        if (type == TYPE_ADD) {
            getViewModel().loadStoryFiles(getViewModelFromActivity().getSelectFiles());
        } else if (type == TYPE_EDIT || type == TYPE__REJECTED_EDIT) {
            getViewModel().loadStoryReEditFiles(getViewModelFromActivity().getStorySelectLiveData().getValue(), getViewModelFromActivity().getReEditSelectId());

            getViewModel().getStoryEditLiveData().observe(this, baseStoryEditItems -> {
                BaseStoryEditItem element = LList.getElement(baseStoryEditItems, 0);
                if (element instanceof VideoStoryEditItem) {
                    // 设置单图/单视频尺寸
                    ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) getDataBinding().storyContent.getLayoutParams();
                    layoutParams.width = dp2px(activity, 192);
                    layoutParams.height = dp2px(activity, 142);
                    layoutParams.leftMargin = dp2px(activity, 20);

                    getDataBinding().ivEditor.setVisibility(View.VISIBLE);
                    getDataBinding().vBgSelector.setVisibility(View.VISIBLE);
                }
            });
        }
        getDataBinding().idLeftIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickLeft(v);
            }
        });

        if (type == TYPE_ADD) { // 新增我的生活图片
            getDataBinding().composeView.setVisibility(View.VISIBLE);
            getViewModel().getStoryEditLiveData().observe(this, baseStoryItems -> StoryListAddComposableKt.bindStoryListAdd(getDataBinding().composeView, getViewModel(), maxImageCount));

            getDataBinding().layoutReportTips.getRoot().setVisibility(View.GONE);
            getDataBinding().qlPlayerContainer.setVisibility(View.GONE);
            getDataBinding().storyContent.setVisibility(View.GONE);
        }

        getDataBinding().etText.requestFocus();
        InputFilter[] inputFilters = new InputFilter[1];
        LengthNoticeFilter filter = new LengthNoticeFilter(50);
        inputFilters[0] = filter;
        filter.setLengthNoticeFilterOverLimitListener(new LengthNoticeFilter.LengthNoticeFilterOverLimitListener() {
            @Override
            public void onOverLimit() {
                getViewModel().setOverLimit(true);
            }

            @Override
            public void onUnderLimit() {
                getViewModel().setOverLimit(false);
            }
        });
        getDataBinding().etText.setFilters(inputFilters);
        TextWatcher watcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String userInput = s.toString();

                getViewModel().setCurrentEditContent(userInput);
                int index = getViewModel().getSelectIndexValue();
                List<BaseStoryEditItem> baseStoryItems = getViewModel().getStoryEditLiveData().getValue();
                //noinspection DataFlowIssue
                if (!LList.isEmpty(baseStoryItems) && index < baseStoryItems.size()) {
                    BaseStoryEditItem baseStoryItem = baseStoryItems.get(index);
                    baseStoryItem.setChangedStoryText(userInput);
                    if (getViewModel().getOriginTextRejectType() == ProfileInfoModel.CONTENT_TYPE_REJECTED) {
                        getViewModel().getTextRejectType().set(TextUtils.equals(s, baseStoryItem.getStoryText()) ? ProfileInfoModel.CONTENT_TYPE_REJECTED : ProfileInfoModel.CONTENT_TYPE_UN_REJECTED);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        };
        getDataBinding().etText.addTextChangedListener(watcher);
        getViewModel().getSelectIndex().observe(this, index -> {
            getDataBinding().etText.removeTextChangedListener(watcher);

            List<BaseStoryEditItem> baseStoryItems = getViewModel().getStoryEditLiveData().getValue();
            //noinspection DataFlowIssue
            if (!LList.isEmpty(baseStoryItems) && index < baseStoryItems.size()) {
                BaseStoryEditItem baseStoryItem = baseStoryItems.get(index);
                String storyText = baseStoryItem.getStoryText();
                String changedStoryText = baseStoryItem.getChangedStoryText();
                String showText;
                if (changedStoryText != null) {
                    showText = changedStoryText;
                } else {
                    showText = storyText;
                }
                if (showText != null) {
                    getDataBinding().etText.setText(showText);
                    getDataBinding().etText.setSelection(showText.length());
                }
            }

            getDataBinding().etText.addTextChangedListener(watcher);
        });

        getDataBinding().etText.postDelayed(() -> getDataBinding().etText.setSelection(getDataBinding().etText.getText().length()), 300);
    }

    private MyInfoEditViewModel model;

    public MyInfoEditViewModel getViewModelFromActivity() {
        return model;
    }

    private void initPagerAdapter() {
        pagerAdapter = new BaseBinderAdapter();
        pagerAdapter.addItemBinder(PicStoryEditItem.class, new BaseDataBindingItemBinder<PicStoryEditItem, MeItemPicStoryBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_pic_story;
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_voice, R.id.tv_reselect);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemPicStoryBinding> holder, @NonNull View view, PicStoryEditItem data, int position) {
                int id = view.getId();
                if (id == R.id.tv_reselect) {
                    reselect();
                }
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemPicStoryBinding> holder, MeItemPicStoryBinding binding, PicStoryEditItem item) {
                getViewModel().getStoryEditLiveData().observe(
                        StoryListEditFragmentNew2.this,
                        baseStoryItems -> StoryListAddComposableKt.bindStoryListAdd(
                                binding.composeView,
                                getViewModel(),
                                1,
                                true, (index) -> {
                                    reselect();
                                    return Unit.INSTANCE;
                                }
                        )
                );
            }
        });
        pagerAdapter.addItemBinder(VideoStoryEditItem.class, new BaseDataBindingItemBinder<VideoStoryEditItem, MeItemVideoStoryCropBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_video_story_crop;
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_voice, R.id.tv_reselect);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemVideoStoryCropBinding> holder, @NonNull View view, VideoStoryEditItem data, int position) {
                int id = view.getId();
                if (id == R.id.iv_voice) {
                    boolean newVoiceStatus = !data.getSilence().get();
                    if (!VideoAudioFloatPageManager.getInstance().hasNoFloatPage() && !newVoiceStatus) {
                        T.ss("您正在通话中，请稍后再试");
                        return;
                    }
                    if (player != null) {
                        data.setSilence(newVoiceStatus);
                        player.setMute(newVoiceStatus);
                    }
                } else if (id == R.id.tv_reselect) {
                    reselect();
                }
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemVideoStoryCropBinding> holder, MeItemVideoStoryCropBinding binding, VideoStoryEditItem item) {
                binding.setBean(item);
            }
        });
        getDataBinding().storyContent.setAdapter(pagerAdapter);
        getDataBinding().storyContent.setUserInputEnabled(false);
        getDataBinding().storyContent.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                play(position);
            }
        });
    }

    private void reselect() {
        AvoidOnResult.Callback callback = (requestCode, resultCode, d) -> {
            if (resultCode == RESULT_OK && d != null) {
                String path = d.getStringExtra(Const.EXTRA_IMAGE_SAVE_PATH);
                if (path != null) {
                    Uri pathUri = Uri.fromFile(new File(path));
                    ArrayList<Uri> uris = new ArrayList<>();
                    uris.add(pathUri);

                    if (LList.getCount(uris) == 1) {
                        Uri source = uris.get(0);
                        if (source != null) {
                            getViewModel().reSelectLoadStoryFiles(Collections.singletonList(source), getViewModel().getSelectIndexValue());
                        }
                    }
                }
            }
        };

        PhotoSelectManager.jumpForGallerySingleCropOnlyResult((FragmentActivity) activity, callback);
    }

    private void play(int position) {
        List<Object> editItems = pagerAdapter.getData();
        if (LList.isEmpty(editItems) || position >= editItems.size()) {
            return;
        }
        Object o = editItems.get(position);
        Log.e(TAG, "play obj=" + o);
        if (o instanceof VideoStoryEditItem video) {
            getDataBinding().svPlayer.setVisibility(View.VISIBLE);
            String path = FileUtils.getPath(getContext(), video.getFile());
            if (!TextUtils.isEmpty(path) && player != null) {
                player.pause();
                player.setMute(video.getSilence().get());
                player.startCache(path);
                player.startPlay(path);
            }
        } else {
            getDataBinding().svPlayer.setVisibility(View.GONE);
            if (player != null) {
                player.pause();
            }
        }
        if (o instanceof BaseStoryEditItem) {
            getViewModel().setCurrentEditContent(((BaseStoryEditItem) o).getChangedStoryText());
        }
    }

    @Override
    public void clickLeft(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        activity.onBackPressed();
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (type == TYPE_ADD) {
            if (getViewModel().getStoryEditLiveData().getValue() != null) {
                getViewModelFromActivity().upLoadAddStoryFile(getViewModel().getStoryEditLiveData().getValue(), () -> {
                    activity.onBackPressed();
                    //noinspection DataFlowIssue
                    return null;
                });
            }
        } else if (type == TYPE_EDIT || type == TYPE__REJECTED_EDIT) {
            if (getViewModel().getStoryEditLiveData().getValue() != null) {
                getViewModelFromActivity().upLoadStoryReEditFile(getViewModel().getStoryEditLiveData().getValue());
            }
            activity.onBackPressed();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        List<Object> baseStoryEditItems = pagerAdapter.getData();
        if (!LList.isEmpty(baseStoryEditItems)) {
            int selectIndex = getViewModel().getSelectIndexValue();
            if (selectIndex < baseStoryEditItems.size()) {
                Object o = baseStoryEditItems.get(selectIndex);
                if (o instanceof VideoStoryEditItem) {
                    if (player != null) {
                        player.resume();
                    }
                }
            }
        }

        QMUIKeyboardHelper.showKeyboard(getDataBinding().etText, true);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (player != null) {
            player.pause();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().unregister(this);
        if (this.player != null) {
            this.player.destroy();
        }
        if (mVideoRender != null) {
            mVideoRender.dispose();
        }
        getDataBinding().svPlayer.release();
    }

    @Override
    public boolean onBackPressed() {
        activity.onBackPressed();
        return true;
    }

    @Override
    public void clickShowGuideDialog() {
        showStoryGuideDialog();
    }

    private void showStoryGuideDialog() {
        StoryGuideDialog.Builder builder = new StoryGuideDialog.Builder((FragmentActivity) activity)
                .setPadding(0, 0)
                .setDisplayTextById(R.id.btn_finish, getResources().getString(R.string.me_i_know))
                .add(R.id.btn_finish)
                .setOnItemClickListener((dialog, view) -> dialog.dismiss());
        builder.create().show();
    }
}