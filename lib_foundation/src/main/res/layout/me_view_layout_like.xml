<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/idChat"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:background="@color/common_color_191919_60"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_radius="22dp">

        <ImageView
            android:layout_gravity="center"
            android:id="@+id/idChatIconWhite"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_preview_chat_white"
            />

    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/llLike"
        android:layout_width="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_height="wrap_content"
        android:background="@color/common_color_191919_60"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/idChat"
        app:qmui_radius="22dp">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/idIconWhite"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:scaleType="centerCrop"
            android:visibility="gone"
            tools:visibility="visible"
            app:lottie_fileName="preview_like/white.json"
             />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/idIconBlack"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:lottie_fileName="preview_like/black.json"
            />

        <TextView
            tools:visibility="visible"
            android:id="@+id/idText"
            android:paddingRight="12dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="38dp"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_16"
            tools:text="99+" />

    </com.qmuiteam.qmui.layout.QMUIFrameLayout>



</androidx.constraintlayout.widget.ConstraintLayout>