<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/common_black_60">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDialogContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@mipmap/me_bg_character_dialog"
            app:layout_constraintDimensionRatio="311:266"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.coorchice.library.SuperTextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:paddingHorizontal="24dp"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:stv_corner="24dp"
                app:stv_shaderEnable="true"
                app:stv_shaderEndColor="#FFFCE8FF"
                app:stv_shaderMode="leftToRight"
                app:stv_shaderStartColor="#FFD6E4FF"
                app:stv_stroke_color="@color/common_color_191919"
                app:stv_stroke_width="2dp"
                tools:text="性格匹配度 99分"
                tools:visibility="gone" />

            <ImageView
                android:id="@+id/ivStar1"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="163dp"
                android:layout_marginTop="14dp"
                android:src="@drawable/me_preivew_chatacter_star"
                app:layout_constraintStart_toStartOf="@id/tvTitle"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/tvDesc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="16dp"
                android:gravity="center_horizontal"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                tools:text="堪称天作之合的搭配，留一分怕你们骄傲堪称天作之合的搭配，留一分怕你们骄傲"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/tvEmptyTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:layout_marginBottom="3dp"
                android:text="你们的恋爱性格"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivStar2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:src="@drawable/me_icon_chatarter_title_star"
                app:layout_constraintBottom_toBottomOf="@id/tvEmptyTitle"
                app:layout_constraintEnd_toStartOf="@id/tvEmptyTitle"
                app:layout_constraintTop_toTopOf="@id/tvEmptyTitle" />

            <ImageView
                android:id="@+id/ivStar3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:src="@drawable/me_icon_chatarter_title_star"
                app:layout_constraintBottom_toBottomOf="@id/tvEmptyTitle"
                app:layout_constraintStart_toEndOf="@id/tvEmptyTitle"
                app:layout_constraintTop_toTopOf="@id/tvEmptyTitle" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.lihang.ShadowLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="34dp"
            android:paddingBottom="15dp"
            app:hl_cornerRadius="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clTitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="450dp"
                android:orientation="vertical">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible">

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/leftType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="23dp"
                        android:layout_marginTop="20dp"
                        android:text="INTJ"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_20"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/leftAnimal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintEnd_toEndOf="@id/leftType"
                        app:layout_constraintStart_toStartOf="@id/leftType"
                        app:layout_constraintTop_toBottomOf="@id/leftType"
                        tools:text="草原狮" />


                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/rightType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="23dp"
                        android:text="INTJ"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_20"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/rightAnimal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintEnd_toEndOf="@id/rightType"
                        app:layout_constraintStart_toStartOf="@id/rightType"
                        app:layout_constraintTop_toBottomOf="@id/rightType"
                        tools:text="草原狮" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rcvResult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="20dp"
                        android:orientation="vertical"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintHeight_max="350dp"
                        app:layout_constraintHeight_min="40dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/leftAnimal"
                        tools:itemCount="3"
                        tools:listitem="@layout/me_preview_character_dialog_item" />


                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>


        </com.lihang.ShadowLayout>


        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/leftAvatar"
            android:layout_width="78dp"
            android:layout_height="78dp"
            android:layout_marginTop="17dp"
            android:layout_marginEnd="60dp"
            app:common_circle="true"
            app:common_image_stroke_color="@color/common_white"
            app:common_image_stroke_width="4dp"
            app:common_src="@mipmap/common_default_avatar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clTitle" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/rightAvatar"
            android:layout_width="78dp"
            android:layout_height="78dp"
            android:layout_marginStart="60dp"
            android:layout_marginTop="17dp"
            app:common_circle="true"
            app:common_image_stroke_color="@color/common_white"
            app:common_image_stroke_width="4dp"
            app:common_src="@mipmap/common_default_avatar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clTitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:src="@drawable/common_white_circle_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clDialogContent" />

</androidx.constraintlayout.widget.ConstraintLayout>