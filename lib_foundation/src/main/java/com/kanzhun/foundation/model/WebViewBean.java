package com.kanzhun.foundation.model;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2020/10/21.
 */
public class WebViewBean implements Serializable {
    private static final long serialVersionUID = -1961878373037365514L;
    public static final int STYLE_HAS_NORMAL = 0;
    public static final int STYLE_SHARE_URL = 4;
    public static final int STYLE_HAS_NO_SHARE = 1;
    public static final int STYLE_HAS_NO_TITLE_AND_TRANSLUCENT = 2;
    protected String path;
    private String name;
    private String url;
    private String title;
    private int style = STYLE_HAS_NORMAL;
    private int requestCode;
    private boolean showBottomLayout;
    private HashMap<String, String> stringParams = new HashMap<>();

    public String getPath() {
        if (!TextUtils.isEmpty(path)) {
            return path;
        }
        return url;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getStyle() {
        return style;
    }

    public void setStyle(int style) {
        this.style = style;
    }

    public void putParams(String key, String value) {
        stringParams.put(key, value);
    }

    public HashMap<String, String> getStringParams() {
        return stringParams;
    }

    public void setRequestCode(int requestCode) {
        this.requestCode = requestCode;
    }

    public int getRequestCode() {
        return requestCode;
    }

    public boolean isShowBottomLayout() {
        return showBottomLayout;
    }

    public void setShowBottomLayout(boolean showBottomLayout) {
        this.showBottomLayout = showBottomLayout;
    }
}
