package com.kanzhun.foundation.model;

import android.text.TextUtils;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.db.DBConstants;
import com.kanzhun.foundation.facade.ContactConverters;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

@Entity(tableName = DBConstants.TAB_CONTACT)
@TypeConverters(ContactConverters.class)
public class Contact implements Serializable {
    private static final long serialVersionUID = -8535111054051335643L;

//    "userId": "xxxx", // 好友id
//            "userType": 1, // 用户类型，1-普通用户，2-系统用户
//            "gender": 1, // 性别，1-男，2-女
//            "nickName": "xx", // 昵称
//            "nickNamePy": "xx", // 昵称拼音
//            "avatar": "https://xxxxx", // 头像
//            "liveVideo":"https://xxxxxxx", // 头像 livePhoto的视频 地址 可空
//            "tinyAvatar": "https://xxxxx", // 头像缩略图
//            "tags": ["官方"], // 用户标签

    @PrimaryKey
    @NotNull
    private String userId; //人员id

    private String securityId;// 安全id，用于透传请求相关接口
    private String nickNamePy;
    private String nickName;
    private String avatar;
    private String tinyAvatar;
    private String moodIcon;
    private String moodTitle;
    private int userType; // 用户类型，1-普通用户，2-系统用户
    private int gender; // 性别，1-男，2-女
    private String liveVideo;
    private List<String> tags;
    private int status;// 1 正常 2 封禁 3 待注销 4 已注销
    private int protectMeetStatus; //0-不展示，1-见面邀约中; 2-见面中;3-已见面

    /**
     *   "modeType":1, // 聊天模式 1 默认 2 见面计划
     *             "modeMinSeq":1003, // 当前聊天模式下最小seq
     */
    private int modeType;
    private long modeMinSeq;

    public int getModeType() {
        return modeType;
    }

    public void setModeType(int modeType) {
        this.modeType = modeType;
    }

    public long getModeMinSeq() {
        return modeMinSeq;
    }

    public void setModeMinSeq(long modeMinSeq) {
        this.modeMinSeq = modeMinSeq;
    }

    /**
     * "profileLocked":1, // 0 未锁定 1 锁定
     */
    private int profileLocked;

    public Contact() {

    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSecurityId() {
        return securityId;
    }

    public void setSecurityId(String securityId) {
        this.securityId = securityId;
    }

    public String getNickNamePy() {
        if (!TextUtils.isEmpty(nickNamePy)) {
            return nickNamePy.replaceAll(" ", "");
        } else {
            return nickNamePy;
        }
    }

    public void setNickNamePy(String nickNamePy) {
        if (!TextUtils.isEmpty(nickNamePy)) {
            this.nickNamePy = nickNamePy.replaceAll(" ", "");
        } else {
            this.nickNamePy = nickNamePy;
        }
    }

    public String getNickName() {
        if (status == Constants.CONTACT_STATUS_DELETED) {
            return "已注销";
        }
        return nickName;
    }

    public int getProtectMeetStatus() {
        return protectMeetStatus;
    }

    public void setProtectMeetStatus(int protectMeetStatus) {
        this.protectMeetStatus = protectMeetStatus;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getTinyAvatar() {
        return tinyAvatar;
    }

    public void setTinyAvatar(String tinyAvatar) {
        this.tinyAvatar = tinyAvatar;
    }

    public String getMoodIcon() {
        return moodIcon;
    }

    public void setMoodIcon(String moodIcon) {
        this.moodIcon = moodIcon;
    }

    public String getMoodTitle() {
        return moodTitle;
    }

    public void setMoodTitle(String moodTitle) {
        this.moodTitle = moodTitle;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getLiveVideo() {
        return liveVideo;
    }

    public void setLiveVideo(String liveVideo) {
        this.liveVideo = liveVideo;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getProfileLocked() {
        return profileLocked;
    }

    public void setProfileLocked(int profileLocked) {
        this.profileLocked = profileLocked;
    }

    public boolean isExceptionStatus() {
        return status == Constants.CONTACT_STATUS_BAN || status == Constants.CONTACT_STATUS_DELETED;
    }
}
