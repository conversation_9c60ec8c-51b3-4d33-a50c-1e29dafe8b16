package com.kanzhun.foundation.model.message;

import android.text.TextUtils;

import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.xxjz.orange.protocol.codec.ChatProtocol;
import com.kanzhun.utils.GsonUtils;

/**
 * create by sunyangyang
 * on 2019-10-23
 */
public class MessageForLinkCall extends ChatMessage {
    private LinkCallInfo mInfo = new LinkCallInfo();
    private LinkCallContentInfo contentInfo = new LinkCallContentInfo();
    private String mNotSupport = "[你有新消息，请升级版本查看]";
    private String mAudioChat = "[语音通话]";
    private String mVideoChat = "[视频通话]";

    public MessageForLinkCall() {
        setMediaType(MessageConstants.MSG_LINK_CALL);
    }

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        ChatProtocol.OgLinkCallMedia linkCallMedia = message.getBody().getLinkCall();
        mInfo = GsonUtils.getGson().fromJson(linkCallMedia.getExt(), LinkCallInfo.class);
        String selfId = AccountHelper.getInstance().getAccount().getUserId();
        String sendId = getSender();
        String content;
        setBadged(false);
        switch (linkCallMedia.getType()) {
            case MessageConstants.LINK_CALL_CANCEL:
                if (TextUtils.equals(selfId, sendId)) {
                    content = "已取消";
                } else {
                    content = "对方已取消";
                    setBadged(true);
                }
                break;
            case MessageConstants.LINK_CALL_REFUSE:
                if (TextUtils.equals(selfId, sendId)) {
                    content = "对方已拒绝";
                } else {
                    content = "已拒绝";
                }
                break;
            case MessageConstants.LINK_CALL_OVER:
                StringBuilder stringBuilder = new StringBuilder("通话时长 ");
                stringBuilder.append(mInfo.time);
                content = stringBuilder.toString();
                break;
            case MessageConstants.LINK_CALL_NOT_CONNECT:
                if (TextUtils.equals(selfId, sendId)) {
                    content = "对方无应答";
                } else {
                    content = "对方已取消";
                    setBadged(true);
                }
                break;
            case MessageConstants.LINK_CALL_NOT_SUPPORT:
                if (TextUtils.equals(selfId, sendId)) {
                    content = "对方版本低";
                } else {
                    content = mNotSupport;
                }
                break;
            case MessageConstants.LINK_CALL_BUSY:
                if (TextUtils.equals(selfId, sendId)) {
                    content = "呼叫失败";
                } else {
                    content = "已拒绝";
                    setBadged(true);
                }
                break;
            default:
                content = mNotSupport;
                break;
        }
        contentInfo.setContent(content);
        contentInfo.setLinkCallMediaType(linkCallMedia.getType());
    }

    @Override
    public void prepare2DB() {
        super.prepare2DB();
        if (contentInfo != null) {
            setContent(GsonUtils.getGson().toJson(contentInfo));
        }
        if (mInfo != null) {
            setExtStr(GsonUtils.getGson().toJson(mInfo));
        }

    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();
        if (!TextUtils.isEmpty(getExtStr())) {
            mInfo = GsonUtils.getGson().fromJson(getExtStr(), LinkCallInfo.class);
        }
        if (!TextUtils.isEmpty(getContent())) {
            contentInfo = GsonUtils.getGson().fromJson(getContent(), LinkCallContentInfo.class);
        }
    }

    public int getLinkCallMediaType() {
        if (contentInfo != null) {
            return contentInfo.getLinkCallMediaType();
        }
        return 0;
    }

    public String getShowContent() {
        if (contentInfo != null) {
            return contentInfo.getContent();
        }
        return "";
    }

    public LinkCallInfo getLinkInfo() {
        return mInfo;
    }

    public void setLinkType(int type) {
        mInfo.setLinkType(type);
    }

    public void setLinkTime(String time) {
        mInfo.setTime(time);
    }

    @Override
    public String getSummary() {
        if (TextUtils.equals(getContent(), mNotSupport)) {
            return mNotSupport;
        }
        return mInfo.getLinkType() == Constants.VIDEO_CHAT_AUDIO ? mAudioChat : mVideoChat;
    }

    public int getLinkType() {
        if (mInfo == null) {
            return Constants.VIDEO_CHAT_AUDIO;
        }
        return mInfo.getLinkType();
    }

    public static class LinkCallInfo {
        private int linkType;
        private String time;

        public int getLinkType() {
            return linkType;
        }

        public void setLinkType(int linkType) {
            this.linkType = linkType;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }
    }

    public static class LinkCallContentInfo {
        private int linkCallMediaType;
        private String content;

        public int getLinkCallMediaType() {
            return linkCallMediaType;
        }

        public void setLinkCallMediaType(int linkCallMediaType) {
            this.linkCallMediaType = linkCallMediaType;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
