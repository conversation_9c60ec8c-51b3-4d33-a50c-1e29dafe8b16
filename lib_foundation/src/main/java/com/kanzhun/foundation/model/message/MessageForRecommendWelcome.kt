package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 推荐欢迎消息
 */
private const val TAG = "MessageForRecommendWelcome"

class MessageForRecommendWelcome : ChatMessage() {
    var recommendWelcome: RecommendWelcome? = null

    init {
        mediaType = MessageConstants.MSG_MEDIA_TYPE_RECOMMEND_WELCOME
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val welcomeMedia = message.body?.recommendWelcome ?: return
            // Parse from protocol buffer message
            recommendWelcome = RecommendWelcome(
                content = welcomeMedia.content,
                suggestMsgList = welcomeMedia.suggestMsgList?.toList() ?: emptyList()
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val welcome = GsonUtils.getGson().fromJson(content, RecommendWelcome::class.java)
        if (welcome != null) {
            recommendWelcome = welcome
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(recommendWelcome)
    }

    override fun getSummary(): String {
        return recommendWelcome?.content ?: "[欢迎消息]"
    }
}

/**
 * 推荐欢迎消息数据结构
 */
data class RecommendWelcome(
    val content: String? = null,           // 开聊文案
    val suggestMsgList: List<String> = emptyList() // 推荐语句列表
) : Serializable
