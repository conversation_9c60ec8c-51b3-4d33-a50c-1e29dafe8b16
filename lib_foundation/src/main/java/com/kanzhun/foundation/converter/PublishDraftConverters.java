package com.kanzhun.foundation.converter;

import android.text.TextUtils;

import androidx.room.TypeConverter;

import com.google.gson.reflect.TypeToken;
import com.kanzhun.foundation.model.PublishDraft;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.base.LList;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class PublishDraftConverters {
    @TypeConverter
    public String ListToString(List<PublishDraft.PublishDraftPicItem> picList) {
        if (LList.isEmpty(picList)) {
            return "";
        }
        try {
            String json = GsonUtils.getGson().toJson(picList);
            if (TextUtils.isEmpty(json)) {
                return "";
            }
            return json;
        } catch (Exception e) {
        }
        return "";
    }

    @TypeConverter
    public List<PublishDraft.PublishDraftPicItem> StringToList(String picS) {
        if (TextUtils.isEmpty(picS)) {
            return new ArrayList<>();
        }
        Type type = new TypeToken<List<PublishDraft.PublishDraftPicItem>>() {
        }.getType();
        try {
            List<PublishDraft.PublishDraftPicItem> list = GsonUtils.getGson().fromJson(picS, type);
            return list;
        } catch (Exception e) {

        }
        return new ArrayList<>();
    }
}
