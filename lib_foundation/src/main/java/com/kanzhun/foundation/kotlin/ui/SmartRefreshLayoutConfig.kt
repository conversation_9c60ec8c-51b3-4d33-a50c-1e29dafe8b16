package com.kanzhun.foundation.kotlin.ui

import com.hpbr.ui.recyclerview.ListData
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.kanzhun.foundation.views.OClassicsFooter
import com.kanzhun.foundation.views.OClassicsHeader


class SmartRefreshLayoutConfig {

    companion object {
        fun init() {
            SmartRefreshLayout.setDefaultRefreshHeaderCreator { context, _ ->
                OClassicsHeader(context)
            }

            SmartRefreshLayout.setDefaultRefreshFooterCreator { context, _ ->
                OClassicsFooter(context)
            }
        }
    }
}

fun <T> SmartRefreshLayout.finish(listData: ListData<T>) {
    if (listData.isRefresh) {
        if(listData.hasMore){
            finishRefresh()
        }else{
            finishRefreshWithNoMoreData()
        }
    } else {
        if (listData.hasMore) {
            finishLoadMore()
        } else {
            finishLoadMoreWithNoMoreData()
        }
    }
}