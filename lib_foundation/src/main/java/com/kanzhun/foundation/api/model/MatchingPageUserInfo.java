package com.kanzhun.foundation.api.model;

import com.kanzhun.foundation.Constants;

import java.io.Serializable;

public class MatchingPageUserInfo implements Serializable {
    private static final long serialVersionUID = -705929226589509108L;

//     "userId": "xxxx", //当前用户id
//             "nickName": "zzzz", // 昵称
//             "avatar": "https://xxxxx", // 头像
//             "tinyAvatar": "https://xxxxx", // 头像缩略图
//             "liveVideo":"https://xxxxxxx", // 头像 livePhoto的视频 地址 可空
//       "status": 1 , // 1 正常 2 封禁 3 待注销 4 已注销
//             "mood": { //心情状态  可空
//        "code": "xxx", // 枚举待定
//                "name": "xxx", // 心情内容
//                "icon": "https://xxxxx", // 心情icon


    public String userId;
    public String nickName;
    public String avatar;
    public String tinyAvatar;
    public String liveVideo;
    public String securityId;
    public MatchingPageMood mood;
    public int status;

    public boolean isDeleted() {
//        return true;
        return status == Constants.CONTACT_STATUS_DELETED;
    }
}
