package com.kanzhun.foundation.api.model;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/7/8
 */
public class UserCertStatusModel {
    /**
     * "faceCert":{
     * "status":2, // 2:已实名 可空
     * },
     * "avatarCert":{
     * "status":3, // 3-通过 可空
     * },
     * "eduCert":{
     * "status": 4, // 4:认证完成  可空
     * "certType":1,// 10 学信网在线验证码 20 认证方式：毕业证/学位证编号 30 认证方式：毕业证/学位证照片 40 认证方式：教留服证书编号  可空
     * },
     * "companyCert":{
     * "status":3, // 3:认证完成 可空
     * "certType":1 // 1 社保截图 2 工卡 可空
     * },
     */

    public UserCertStatusBean faceCert;
    public UserCertStatusBean avatarCert;
    public UserCertStatusBean eduCert;
    public UserCertStatusBean companyCert;

    public UserCertStatusBean carCert;
    public UserCertStatusBean houseCert;
    public UserCertStatusBean incomeCert;

    public static class UserCertStatusBean {
        public int status;
        public int certType;
    }
}
