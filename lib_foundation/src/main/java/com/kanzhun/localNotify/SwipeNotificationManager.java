package com.kanzhun.localNotify;

import static android.os.Build.VERSION_CODES.R;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.foundation.logic.service.SecurityNoticeManager;
import com.petterp.floatingx.FloatingX;
import com.petterp.floatingx.assist.FxDisplayMode;
import com.petterp.floatingx.assist.FxGravity;
import com.petterp.floatingx.assist.FxScopeType;
import com.petterp.floatingx.assist.helper.FxAppHelper;
import com.petterp.floatingx.assist.helper.FxScopeHelper;
import com.techwolf.lib.tlog.TLog;

import java.util.LinkedList;


public class SwipeNotificationManager {

    private static final String TAG = "FloatingWindowManager";

    private static transient SwipeNotificationManager sManager;

    private Context mContext;

    // 最大允许显示的通知条目数量
    private LinkedList<LocalNotifyMessageBean> linkedList = new LinkedList<>();
    private transient boolean stop;

    private SwipeNotificationManager(Context context) {
        mContext = context;
        // 新建一个包含所有通知的容器
        init(context);
    }

    public static SwipeNotificationManager getInstance(Context context) {
        if (sManager == null) {
            synchronized (SwipeNotificationManager.class) {
                if (sManager == null) {
                    sManager = new SwipeNotificationManager(context.getApplicationContext());
                }
            }
        }
        return sManager;
    }

    public synchronized void addNotification(LocalNotifyMessageBean localNotifyMessageBean) {
        TLog.info(TAG, "addNotification ---------1");
        if (stop) return;
        TLog.info(TAG, "addNotification ---------2 linkedList:"+linkedList.toString());
        TLog.info(TAG, "addNotification ---------2 addNotification size:"+linkedList.size());
        linkedList.addLast(localNotifyMessageBean);
        TLog.info(TAG, "addNotification ---------3 addNotification size:"+linkedList.size());
        TLog.info(TAG, "addNotification ---------2 linkedList:"+linkedList.toString());
        notifyItemNotify();
    }

    public synchronized void stop() {
        TLog.info(TAG, "stop ---------1");
        stop = true;
        TLog.info(TAG, "stop ---------2");
        clear();
        TLog.info(TAG, "stop ---------3");
    }

    public boolean isStop() {
        return stop;
    }

    public synchronized void clear() {
        try {
            TLog.info(TAG, "clear ---------1");
            TLog.info(TAG, "clear ---------2 linkedList:"+linkedList.toString());
            linkedList.clear();
            TLog.info(TAG, "clear ---------2");
            TLog.info(TAG, "clear ---------3");
            TLog.info(TAG, "clear ---------4");
            FloatingX.control(TAG).hide();
        } catch (Exception e) {

        } finally {

        }
    }

    public synchronized void reStart() {
        if (SecurityNoticeManager.INSTANCE.isBlock())return;
        TLog.info(TAG, "reStart ---------1");
        stop = false;
    }
    private LinearLayout mNotificationContainer;

    private synchronized void notifyItemNotify() {
        TLog.info(TAG, "notifyItemNotify ---------1 stop:" + stop);
        if (stop) {
            return;
        }
        TLog.info(TAG, "notifyItemNotify ---------2 notifyItemNotify size:" + linkedList.size());
        if (!linkedList.isEmpty()) {
            if (!FloatingX.control(TAG).isShow()) {
                LocalNotifyMessageBean bean = linkedList.removeFirst();
                TLog.info(TAG, "notifyItemNotify ---------2 notifyItemNotify size:" + linkedList.size());
                // 新建一条通知
                mNotificationContainer = new LinearLayout(mContext);
                mNotificationContainer.setOrientation(LinearLayout.VERTICAL);
                final SwipeNotification notification = new SwipeNotification(mContext);
                mNotificationContainer.addView(notification,new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT));
                mNotificationContainer.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT));
                notification.setData(bean);

                // 通知已经通过滑动移除出屏幕的时候，将通知从 mNotificationContainer 中移除
                notification.setOnDisappearListener(new SwipeNotification.OnDisappearListener() {
                    @Override
                    public void onDisappear() {
                        // 如果 mNotificationContainer 里面已经没有通知了，将 mNotificationContainer 从窗口移除
                        TLog.info(TAG, "notifyItemNotify ---------3 onDisappear size:" + linkedList.size());
                        FloatingX.control(TAG).hide();
                        if (linkedList.isEmpty()) {

                        } else {
                            notifyItemNotify();
                        }
                    }
                });
                FloatingX.control(TAG).updateView(mNotificationContainer);

                FloatingX.control(TAG).show();
            }
        }else {
            FloatingX.control(TAG).hide();
        }
    }

    private void init(Context context){
        mNotificationContainer = new LinearLayout(mContext);
        mNotificationContainer.setOrientation(LinearLayout.VERTICAL);
        mNotificationContainer.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT));
        FxAppHelper helper = FxAppHelper.builder()
                .setLayoutView(mNotificationContainer)
                .setContext(context)
                .setScopeType(FxScopeType.APP)
                // 设置启用日志,tag可以自定义，最终显示为FloatingX-xxx
                .setEnableLog(true, TAG)

                //1. 是否允许全局显示悬浮窗,默认true
//                .setEnableAllInstall(true)
                //2. 禁止插入Activity的页面, setEnableAllBlackClass(true)时,此方法生效
//                .addInstallBlackClass(BlackActivity.class)
                //3. 允许插入Activity的页面, setEnableAllBlackClass(false)时,此方法生效
//                .addInstallWhiteClass(MainActivity.class, ScopeActivity.class)

                // 设置启用边缘吸附
                .setEnableEdgeAdsorption(false)
                // 设置边缘偏移量
                .setEdgeOffset(0f)
                .setGravity(FxGravity.LEFT_OR_TOP)
                // 设置启用悬浮窗可屏幕外回弹
                .setEnableScrollOutsideScreen(false)
                .setManagerParams(new FrameLayout.LayoutParams(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT))
                .setTag(TAG)
                // 设置辅助方向辅助
                // 设置点击事件
//                .setOnClickListener()
                // 设置view-lifecycle监听
//            setViewLifecycle()
                // 设置启用动画
                .setEnableAnimation(false)
                // 设置启用动画实现
//                .setAnimationImpl(new FxAnimationImpl())
                // 设置方向保存impl
//                .setSaveDirectionImpl(new FxConfigStorageToSpImpl(this))

                // 设置底部偏移量
//                .setBottomBorderMargin(100f)
                // 设置顶部偏移量
//            setTopBorderMargin(100f)
                // 设置左侧偏移量
//                .setLeftBorderMargin(100f)
                // 设置右侧偏移量
//                .setRightBorderMargin(100f)
                // 设置浮窗展示类型，默认可移动可点击，无需配置
                .setDisplayMode(FxDisplayMode.ClickOnly)
                //启用悬浮窗,即默认会插入到允许的activity中
                // 启用悬浮窗,相当于一个标记,会自动插入允许的activity中
                .build();
        FloatingX.install(helper);
    }

}
