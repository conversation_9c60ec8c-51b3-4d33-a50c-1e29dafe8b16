package com.kanzhun.marry.social.viewmodel;

import android.app.Application;
import android.content.ContentUris;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.ArrayMap;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.dialog.model.SelectBottomBean;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.PublishDraft;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.api.SocialApi;
import com.kanzhun.foundation.api.bean.MomentModel;
import com.kanzhun.marry.social.model.PublishPicBean;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.file.FileUtils;
import com.zhihu.matisse.internal.entity.Item;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;
import top.zibin.luban.Luban;

public class PublishViewModel extends FoundationViewModel {
    private static final String TAG = "PublishViewModel";
    private MutableLiveData<List<PublishPicBean>> picLiveData = new MutableLiveData<>();
    private MutableLiveData<MomentModel> publishLiveData = new MutableLiveData<>();
    private MutableLiveData<String> editRenderDraftLiveData = new MutableLiveData<>();
    private ObservableField<String> editContent = new ObservableField<String>("");
    private ObservableInt imageNum = new ObservableInt(0);
    private ObservableField<String> hint = new ObservableField<String>("");
    private ObservableBoolean showVisualRange = new ObservableBoolean(true);
    private ObservableBoolean syncPersonal = new ObservableBoolean(false);
    private String id;
    private int publishType;
    private ObservableInt visibleType = new ObservableInt(Constants.SOCIAL_VISIBLE_TYPE_ALL);
    private ObservableField<String> visibleTypeS = new ObservableField<>(getResources().getString(R.string.social_visual_all));
    List<SelectBottomBean> list = new ArrayList();
    private MutableLiveData<ArrayList<Item>> selectedPicLiveData = new MutableLiveData<>();

    public PublishViewModel(Application application) {
        super(application);
        Random random = new Random();
        int index = random.nextInt(4);
        String[] stringArray = getResources().getStringArray(R.array.social_array_publish_hint);
        hint.set(stringArray[index]);
        list.add(new SelectBottomBean(Constants.VISUAL_ALL, getResources().getString(R.string.social_visual_all), R.drawable.social_select_visual_range_black));
        list.add(new SelectBottomBean(Constants.VISUAL_FRIEND, getResources().getString(R.string.social_visual_friend), R.drawable.social_ic_visible_firend_black));
    }

    public void init(String circleId, int publishType) {
        this.id = circleId;
        this.publishType = publishType;
        showVisualRange.set(this.publishType != Constants.PUBLISH_TYPE_CIRCLE);
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                PublishDraft draft = ServiceManager.getInstance().getSocialService().findByUid(StringUtil.getPublishDraftId(publishType, id));
                if (draft != null) {
                    syncPersonal.set(draft.getSyncPersonal() == Constants.SYNC_PERSONAL);
                    if (!TextUtils.isEmpty(draft.getContent())) {
                        editRenderDraftLiveData.postValue(draft.getContent());
                    }
                    int visibleType = draft.getVisibleType();
                    if (Constants.SOCIAL_VISIBLE_TYPE_ALL == visibleType) {
                        PublishViewModel.this.visibleType.set(visibleType);
                        visibleTypeS.set(getResources().getString(R.string.social_visual_all));
                    } else if (Constants.SOCIAL_VISIBLE_TYPE_FRIEND == visibleType) {
                        PublishViewModel.this.visibleType.set(visibleType);
                        visibleTypeS.set(getResources().getString(R.string.social_visual_friend));
                    }

                    if (!LList.isEmpty(draft.getPictures())) {
                        List<PublishPicBean> result = new ArrayList<>();
                        for (PublishDraft.PublishDraftPicItem draftPicItem : draft.getPictures()) {
                            if (TextUtils.isEmpty(draftPicItem.getCompressedUriS())) {
                                continue;
                            }
                            Uri compressUri = Uri.parse(draftPicItem.getCompressedUriS());
                            if (compressUri == null) {
                                continue;
                            }
                            Uri originUri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, draftPicItem.getId());
                            if (FileUtils.uriFileExist(getApplication(), compressUri) && FileUtils.uriFileExist(getApplication(), originUri)) {
                                PublishPicBean bean = new PublishPicBean(draftPicItem.getId(), draftPicItem.getMimeType(), draftPicItem.getSize(), draftPicItem.getDuration(), compressUri);
                                result.add(bean);
                            }
                        }
                        picLiveData.postValue(result);
                    }
                }
            }
        });
    }

    public String getId() {
        return id;
    }

    public int getPublishType() {
        return publishType;
    }

    public ObservableField<String> getEditContent() {
        return editContent;
    }

    public ObservableInt getImageNum() {
        return imageNum;
    }

    public ObservableField<String> getHint() {
        return hint;
    }

    public ObservableBoolean getShowVisualRange() {
        return showVisualRange;
    }

    public MutableLiveData<List<PublishPicBean>> getPicLiveData() {
        return picLiveData;
    }

    public MutableLiveData<MomentModel> getPublishLiveData() {
        return publishLiveData;
    }

    public MutableLiveData<String> getEditRenderDraftLiveData() {
        return editRenderDraftLiveData;
    }

    public ObservableInt getVisibleType() {
        return visibleType;
    }

    public ObservableField<String> getVisibleTypeS() {
        return visibleTypeS;
    }

    public List<SelectBottomBean> getVisibleTypeList() {
        return list;
    }

    public MutableLiveData<ArrayList<Item>> getSelectedPicLiveData() {
        return selectedPicLiveData;
    }

    public ObservableBoolean getSyncPersonal() {
        return syncPersonal;
    }

    public void setVisibleType(int visibleType, String content) {
        this.visibleType.set(visibleType);
        this.visibleTypeS.set(content);
    }

    public void loadPicData(List<Item> source) {
        setShowProgressBar("", false);
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                List<PublishPicBean> result = new ArrayList<>();
                if (!LList.isEmpty(source)) {
                    try {
                        for (Item i : source) {
                            Uri originalUri = i.getContentUri();
                            if (originalUri != null) {
                                Uri compressUri = Luban.with(getApplication()).ignoreBy(1024*10).getSingleCompressUri(originalUri);
                                if (compressUri != null) {
                                    result.add(new PublishPicBean(i.id, i.mimeType, i.size, i.duration, compressUri));
                                }
                            }
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                picLiveData.postValue(result);
                hideShowProgressBar();
            }
        });

    }

    public List<String> getImageUrisString() {
        List<String> data = new ArrayList<>();
        List<PublishPicBean> cache = picLiveData.getValue();
        if (!LList.isEmpty(cache)) {
            for (PublishPicBean bean : cache) {
                if (bean.getCompressUri() != null) {
                    data.add(bean.getCompressUri().toString());
                }
            }
        }
        return data;
    }

    public List<? extends Object> getShowPicData() {
        List<Object> pics = new ArrayList<>();
        List<PublishPicBean> cache = picLiveData.getValue();
        if (!LList.isEmpty(cache)) {
            pics.addAll(cache);
        }
        if (pics.size() < 6) {
            pics.add(null);
        }
        return pics;
    }

    public void sort(List sorts) {
        if (LList.isEmpty(sorts)) {
            return;
        }
        List<PublishPicBean> sortList = new ArrayList<>();
        for (Object sort : sorts) {
            if (sort instanceof PublishPicBean) {
                sortList.add((PublishPicBean) sort);
            }
        }
        picLiveData.postValue(sortList);
    }

    public void deletePic(PublishPicBean delBean) {
        List<PublishPicBean> cache = picLiveData.getValue();
        if (!LList.isEmpty(cache)) {
            List<PublishPicBean> result = new ArrayList<>();
            for (PublishPicBean bean : cache) {
                if (bean != delBean) {
                    result.add(bean);
                }
            }
            picLiveData.postValue(result);
        }
    }

    public void requestSubmit() {
        setShowProgressBar("", false);
        List<PublishPicBean> images = getPicLiveData().getValue();
        if (LList.isEmpty(images)) {
            publish("");
        } else {
            ExecutorFactory.execLocalTask(new Runnable() {
                @Override
                public void run() {
                    boolean hasEmptyToken = false;
                    StringBuilder imgUrls = new StringBuilder();
                    for (PublishPicBean p : images) {
                        if (TextUtils.isEmpty(p.getToken())) {
                            requestUploadPic(p);
                            hasEmptyToken = true;
                        } else {
                            imgUrls.append(p.getToken()).append(",");
                        }
                    }
                    if (!hasEmptyToken) {
                        if (!TextUtils.isEmpty(imgUrls)) {
                            imgUrls.deleteCharAt(imgUrls.length() - 1);
                        }
                        publish(imgUrls.toString());
                    }
                }
            });
        }
    }

    private void requestUploadPic(PublishPicBean publishPicBean) {
        Uri uri = publishPicBean.getCompressUri();
        if (uri != null && FileUtils.uriFileExist(getApplication(), uri)) {
            UploadFileUtil.uploadImage(UploadFileUtil.COMMUNITY, uri, new UploadRequestCallback<ImageUploadModel>() {
                @Override
                public void onStart(Disposable disposable) {
                    super.onStart(disposable);
                }

                @Override
                public void onSuccess(ImageUploadModel data) {
                    if (data != null) {
                        publishPicBean.setToken(data.token);
                    }
                    List<PublishPicBean> images = getPicLiveData().getValue();
                    if (!LList.isEmpty(images)) {
                        StringBuilder imgUrls = new StringBuilder();
                        for (PublishPicBean p : images) {
                            if (TextUtils.isEmpty(p.getToken())) {
                                return;
                            }
                            imgUrls.append(p.getToken()).append(",");
                        }
                        if (!TextUtils.isEmpty(imgUrls)) {
                            imgUrls.deleteCharAt(imgUrls.length() - 1);
                        }
                        publish(imgUrls.toString());
                    }

                }

                @Override
                public void dealFail(ErrorReason reason) {
                    hideShowProgressBar();
                    T.ss(reason.getErrReason());
                }
            });
        } else {
            hideShowProgressBar();
        }
    }

    /**
     * 发布
     */
    private void publish(String imgUrls) {
        ArrayMap<String, Object> map = new ArrayMap<>();
        if (TextUtils.isEmpty(editContent.get())) {
            map.put("content", "");
        } else {
            map.put("content", StringUtil.trimEnd(editContent.get()));
        }
        if (!TextUtils.isEmpty(imgUrls)) {
            map.put("pictures", imgUrls);
        }
        map.put("visibleType", visibleType.get());
        if (!TextUtils.isEmpty(id)) {
            map.put("circleId", id);
            map.put("syncPersonal", syncPersonal.get() ? Constants.SYNC_PERSONAL : Constants.UN_SYNC_PERSONAL);
        }
        Observable<BaseResponse<MomentModel>> observable = RetrofitManager.getInstance().createApi(SocialApi.class).publish(map);
        HttpExecutor.execute(observable, new BaseRequestCallback<MomentModel>(true) {

            @Override
            public void onSuccess(MomentModel data) {
                publishLiveData.postValue(data);
                deleteDraft();
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    /**
     * 保存草稿
     */
    public void saveDraft() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                PublishDraft draft = new PublishDraft();
                String content = editContent.get();
                if (!TextUtils.isEmpty(content)) {
                    draft.setContent(content);
                }
                draft.setVisibleType(visibleType.get());
                draft.setId(StringUtil.getPublishDraftId(publishType, id));
                draft.setSyncPersonal(syncPersonal.get() ? Constants.SYNC_PERSONAL : Constants.UN_SYNC_PERSONAL);
                List<PublishPicBean> cache = picLiveData.getValue();
                if (!LList.isEmpty(cache)) {
                    List<PublishDraft.PublishDraftPicItem> pictures = new ArrayList<>();
                    for (PublishPicBean bean : cache) {
                        if (bean.getCompressUri() != null) {
                            PublishDraft.PublishDraftPicItem picItem = new PublishDraft.PublishDraftPicItem();
                            picItem.setId(bean.getId());
                            picItem.setMimeType(bean.getMimeType());
                            picItem.setSize(bean.getSize());
                            picItem.setDuration(bean.getDuration());
                            picItem.setCompressedUriS(bean.getCompressUri().toString());
                            pictures.add(picItem);
                        }
                    }
                    draft.setPictures(pictures);
                }
                ServiceManager.getInstance().getSocialService().insert(draft);
            }
        });

    }

    public void deleteDraft() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ServiceManager.getInstance().getSocialService().deletePublishDraftById(StringUtil.getPublishDraftId(publishType, id));
            }
        });
    }

    public void addWithSelectPics() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                List<PublishPicBean> cache = picLiveData.getValue();
                ArrayList<Item> select = new ArrayList<>();
                if (!LList.isEmpty(cache)) {
                    for (PublishPicBean p : cache) {
                        if (fileExist(p)) {
                            Item item = new Item(p.getId(), p.getMimeType(), p.getSize(), p.getDuration());
                            select.add(item);
                        }
                    }
                }
                selectedPicLiveData.postValue(select);
            }
        });
    }

    public boolean fileExist(PublishPicBean publishPicBean) {
        Uri originUri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, publishPicBean.getId());
        return FileUtils.uriFileExist(getApplication(), originUri);
    }
}