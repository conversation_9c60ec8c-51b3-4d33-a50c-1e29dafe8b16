package com.kanzhun.marry.matching.fragment.home.performance

import androidx.lifecycle.LifecycleOwner
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.matching.adpter.home.MatchingHomeViewHolderFactory
import com.kanzhun.marry.matching.api.model.CardNewType
import com.kanzhun.marry.matching.api.model.FilmBean
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.api.model.TomorrowRecommendData
import com.kanzhun.marry.matching.fragment.home.MatchHomePagerFragment
import com.kanzhun.marry.matching.utils.point.MatchPointReporter
import com.youth.banner.listener.OnPageChangeListener

/**
 * 孩子首页各种曝光埋点逻辑
 */
class MatchingHomeExposePerformance(val mFragment: MatchHomePagerFragment) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        mFragment.mBinding.viewPager.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

            }

            override fun onPageSelected(position: Int) {
                exposeCard(position)
                mFragment.updateNum(position)
                mFragment.updateBg(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
            }

        })
    }



    fun exposeCard(position: Int){
        val list = mFragment.mViewModel.recommendList.value?.list ?: mutableListOf()
        if (list.isEmpty() || position >= list.size) {
            return
        }
        val item = list[position]
        //曝光埋点
        reportExpose(item,position)
        //后端业务需要，卡片曝光
        mFragment.mViewModel.run {
            reportCardExpose(item)
        }
    }
    private fun reportExpose(item: IPageBean?,position: Int) {

        when (item?.getItemType()) {
            MatchingHomeViewHolderFactory.TYPE_RECOMMEND_USER -> { //正常用户
                if(item is RecommendUser){
                    if(item.getBlock()){
                        MatchPointReporter.reportChildNewGuidanceF1LockedCardExpose(item.baseInfo?.encUserId)
                    }else{
                        MatchPointReporter.reportF1RecommendUserCardExpose(item.baseInfo?.encUserId,item.lid,position,item.baseInfo?.relationStatus?:0,
                            item.sameActivity?.showText ?:"",item.cacheTs.toString(),item.recommendInfo?.content?:"",item.recommendInfo?.score?:"")
                    }
                }
            }

            MatchingHomeViewHolderFactory.TYPE_RECOMMEND_TOMORROW -> { //明日预告
                if(item is TomorrowRecommendData){
                    MatchPointReporter.reportF1TomorrowCardExpose(item.userList?.map { it.baseInfo?.encUserId?:"" })
                }
            }

            MatchingHomeViewHolderFactory.TYPE_NEW_USER -> { //新用户引导
                if(item is CardNewType){
                    reportPoint("guide-card-activity-expo"){
                        actionp2 = item.name
                        type = item.type.toString()
                    }
                }
            }

            MatchingHomeViewHolderFactory.TYPE_USER_GUIDE_BLOCK -> { //新手阻断卡片

            }

            MatchingHomeViewHolderFactory.TYPE_USER_FILM -> { //新手阻断卡片

                reportPoint("F1-film-activities-entrance-expo"){
                    actionp2 = if(item is FilmBean) item.picUrl else ""
                    actionp3 = "2"
                }
            }

            else -> {

            }
        }
    }
}