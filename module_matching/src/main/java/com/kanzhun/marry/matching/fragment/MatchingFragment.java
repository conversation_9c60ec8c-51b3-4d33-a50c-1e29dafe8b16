package com.kanzhun.marry.matching.fragment;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;

import com.kanzhun.common.animator.BaseViewAnimator;
import com.kanzhun.common.animator.YoYo;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;
import com.kanzhun.common.kotlin.ext.ActivityExtKt;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.bean.event.ClickMatchEvent;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.matching.MatchingBlockInfoModel;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.callback.MatchingCallback;
import com.kanzhun.marry.matching.databinding.MatchingFragmentMatchingBinding;
import com.kanzhun.marry.matching.viewmodel.MatchingViewModel;
import com.kanzhun.marry.matching.views.MatchingRefreshFooter;
import com.kanzhun.marry.matching.views.MatchingUserInfoGuide;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

/**
 * 匹配个人页推荐
 */
public class MatchingFragment extends FoundationVMFragment<MatchingFragmentMatchingBinding, MatchingViewModel>
        implements MatchingCallback, OnRefreshListener, OnLoadMoreListener {

    private boolean isFirstLoad = true;
    private boolean showGuided;
    private boolean canShowGuideHasNotShow = false;//可以显示蒙层但是没有展示蒙层
    private Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (msg.what == 1) {
                setLoadingUI();
            }
            return false;
        }
    });

    private int firstTabCheckedIndex = Constants.F1_RECOMMEND_INDEX;

    @Override
    protected void initFragment() {
        super.initFragment();
        initView();
        initData();
    }

    private void initData() {
        getViewModel().getMatchTypeLiveData().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer != null) {
                    setDate(integer);
                }
            }
        });
        RxBus.getInstance().subscribe(this, Constants.MATCHING_USER_INFO, new RxBus.Callback<MatchingBlockInfoModel>() {
            @Override
            public void onEvent(MatchingBlockInfoModel blockInfoModel) {
                if (blockInfoModel != null) {
                    setMatchingBlockInfoFragmentForLike(blockInfoModel);
                }
            }
        });

        RxBus.getInstance().subscribe(this, Constants.MATCHING_USER_INFO, new RxBus.Callback<String>() {
            @Override
            public void onEvent(String content) {
                if (TextUtils.equals(content, "clickLater")) {
                    setMatchingUserInfoFragmentForClickLater();
                } else if (TextUtils.equals(content, "refreshMatchStatus")) {
                    setMatchingUserInfoFragmentForLike();
                }
            }
        });
        RxBus.getInstance().subscribe(this, Constants.MATCHING_USER_INFO, new RxBus.Callback<ClickMatchEvent>() {
            @Override
            public void onEvent(ClickMatchEvent clickLikeEvent) {
                if (clickLikeEvent == null) {
                    return;
                }
                if (TextUtils.equals(ClickMatchEvent.MATCH_LIKE_CLICK, clickLikeEvent.tag)) {
                    if (TextUtils.equals(clickLikeEvent.userId, getViewModel().getInfoModel().userId)) {
                        setMatchingUserInfoFragmentForLike();
                    }
                } else if (TextUtils.equals(ClickMatchEvent.MATCH_REPORT_CLICK, clickLikeEvent.tag)) {
                    if (TextUtils.equals(clickLikeEvent.userId, getViewModel().getInfoModel().userId)) {
                        setMatchingUserInfoFragmentForLike();
                    }
                }

            }
        });
        RxBus.getInstance().subscribe(this, Constants.POST_TAG_FIRST_TAB_CHECKED_INDEX, new RxBus.Callback<Integer>() {
            @Override
            public void onEvent(Integer index) {
                if (index != null) {
                    firstTabCheckedIndex = index;
                    if (index == Constants.F1_RECOMMEND_INDEX && canShowGuideHasNotShow) {
                        showGuidePop();
                    }
                }
            }
        });
//        RxBus.getInstance().subscribe(this, Constants.POST_TAG_MAIN_TAB_SELECTED_INDEX, new RxBus.Callback<Integer>() {
//            @Override
//            public void onEvent(Integer index) {
//
//            }
//        });
        ActivityExtKt.liveEventObserve(this, Constants.POST_TAG_MAIN_TAB_SELECTED_INDEX, new Observer<Integer>() {
            @Override
            public void onChanged(Integer index) {
                T.ss("收到了："+index);
                if (index != null) {
                    if (index == 0 && firstTabCheckedIndex == Constants.F1_RECOMMEND_INDEX && canShowGuideHasNotShow) {
                        showGuidePop();
                    }
                }
            }
        });
    }

    private void setDate(int type) {
        switch (type) {
            case MatchingViewModel.TYPE_USER_INFO: {
                //表示匹配个人页面
                getDataBinding().refreshLayout.finishLoadMore();
                getDataBinding().refreshLayout.finishRefresh();
                setMatchingUserInfoFragment();
                getViewModel().setClickNext(false);
            }
            break;
            case MatchingViewModel.TYPE_FINISH_INFO: {
                //表示看完所有推荐人完成页面
                getDataBinding().refreshLayout.finishLoadMore();
                getDataBinding().refreshLayout.finishRefresh();
                setMatchingFinishFragment();
                getViewModel().setClickNext(false);
            }
            break;
            case MatchingViewModel.TYPE_BLOCK_INFO: {
                //表示阻断页面
                getDataBinding().refreshLayout.finishLoadMore();
                getDataBinding().refreshLayout.finishRefresh();
                setMatchingBlockInfoFragment();
                getViewModel().setClickNext(false);
                break;
            }
            case MatchingViewModel.TYPE_ABOVE_FINISH_INFO: {
                //表示没有上一个
                getDataBinding().refreshHeader.setErrorText(getViewModel().getFinishTitle());
                getDataBinding().refreshLayout.finishLoadMore(false);
                getDataBinding().refreshLayout.finishRefresh(false);
            }
            break;
            case MatchingViewModel.TYPE_USER_INFO_ERROR: {
                getDataBinding().refreshLayout.finishLoadMore();
                getDataBinding().refreshLayout.finishRefresh();
                handler.removeMessages(1);
                if (getViewModel().isFirstLoadData() || getViewModel().isClickNext()) {
                    setLoadingUIError();
                } else if (getViewModel().getMatchType() == MatchingViewModel.TYPE_NEXT_TYPE) {
                    if (getDataBinding().flNext.getVisibility() != View.VISIBLE) {
                        getDataBinding().flNext.setVisibility(View.VISIBLE);
                    }
                }
                break;
            }
            default:
                break;
        }

        revealLoadingUI();
    }

    /**
     * 兜底 某些case 不该出现loadingui 但是出现了
     */
    private void revealLoadingUI() {
        getDataBinding().flLoading.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (getDataBinding().flLoading.getVisibility() == View.VISIBLE && getDataBinding().icLoading.btnRetry.getVisibility() == View.GONE) {
                    getDataBinding().flLoading.setVisibility(View.GONE);
                }
            }
        }, 400);
    }

    private void setLoadingUI() {
        getDataBinding().flNext.setVisibility(View.GONE);
        getDataBinding().flLoading.setVisibility(View.VISIBLE);
        getDataBinding().flLoading.setAlpha(1.0f);
        getDataBinding().icLoading.btnRetry.setVisibility(View.GONE);
//        getDataBinding().icLoading.avLoad.setVisibility(View.VISIBLE);
        getDataBinding().icLoading.avLoadBg.setVisibility(View.VISIBLE);
//        getDataBinding().icLoading.avLoad.setRepeatCount(ValueAnimator.INFINITE);
//        getDataBinding().icLoading.avLoad.playAnimation();
        getDataBinding().icLoading.ivError.setVisibility(View.INVISIBLE);
        getDataBinding().icLoading.tvDesc.setText(getResources().getString(R.string.common_loading));
    }

    private void setLoadingUIError() {
        getDataBinding().refreshLayout.setEnableRefresh(false);
        getDataBinding().refreshLayout.setEnableLoadMore(false);
        getDataBinding().flNext.setVisibility(View.GONE);
        getDataBinding().flLoading.setVisibility(View.VISIBLE);
        getDataBinding().flLoading.setAlpha(1.0f);
//        getDataBinding().icLoading.avLoad.setVisibility(View.GONE);
        getDataBinding().icLoading.avLoadBg.setVisibility(View.GONE);
        getDataBinding().icLoading.btnRetry.setVisibility(View.VISIBLE);
        getDataBinding().icLoading.ivError.setVisibility(View.VISIBLE);
        getDataBinding().icLoading.tvDesc.setText(getViewModel().getFinishTitle());
    }

    /**
     * 加载中布局动画
     */
    private void loadingAnimator() {
        if (getDataBinding().flLoading.getVisibility() == View.VISIBLE) {
            YoYo.with(new BaseViewAnimator() {
                        @Override
                        protected void prepare(View target) {
                            getAnimatorAgent().playTogether(
                                    ObjectAnimator.ofFloat(target, "alpha", 1, 0));
                        }
                    }).duration(300).interpolate(InterpolatorUtil.createDefaultOvershootInterpolator()).
                    withListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            getDataBinding().flLoading.setVisibility(View.GONE);
                        }
                    }).playOn(getDataBinding().flLoading);
        }
    }

    /**
     * 加载匹配个人页
     */
    private void setMatchingUserInfoFragment() {
        MatchingUserInfoFragment fragment = new MatchingUserInfoFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA, getViewModel().getInfoModel());
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN_1, true);
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 10);
        fragment.setArguments(bundle);
        if (getViewModel().getMatchType() == MatchingViewModel.TYPE_NEXT_TYPE) {
            if (!getViewModel().isFirstLoadData()) {
                if (getViewModel().isClickNext()) {
                    handler.removeMessages(1);
                    loadingAnimator();
                    getDataBinding().switcherLayout.setOnlyNextFragment(fragment, new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationStart(Animator animation) {
                            if (getDataBinding().flNext.getVisibility() == View.GONE) {
                                nextIconAnimator();
                            }
                        }

                        @Override
                        public void onAnimationEnd(Animator animation) {
                            getDataBinding().refreshLayout.setEnableLoadMore(true);
                            getDataBinding().refreshLayout.setEnableRefresh(true);
                        }
                    });
                } else {
                    getDataBinding().switcherLayout.setNextFragment(fragment, new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationStart(Animator animation) {
                            //出现时 下一个按钮做动画
                            if (showGuided) {
                                nextIconAnimator();
                            }
                        }

                        @Override
                        public void onAnimationEnd(Animator animation) {
                            getDataBinding().refreshLayout.setEnableLoadMore(true);
                            getDataBinding().refreshLayout.setEnableRefresh(true);
                        }
                    });
                }
            } else {
                //是否是第一次加载
                getViewModel().setFirstLoadData(false);
                loadingAnimator();
                getDataBinding().switcherLayout.setCurrentFragment(fragment, new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        //出现时 下一个按钮做动画
                        if (showGuided) {
                            nextIconAnimator();
                        }
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        //结束时需要判断时候有蒙层
                        if (!showGuided) {
                            handleUserInfoGuideShow();
                        }
                        getDataBinding().refreshLayout.setEnableLoadMore(true);
                        getDataBinding().refreshLayout.setEnableRefresh(true);
                    }
                });
            }

        } else {
            getDataBinding().switcherLayout.setAboveFragment(fragment, new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    getDataBinding().refreshLayout.setEnableLoadMore(true);
                    getDataBinding().refreshLayout.setEnableRefresh(true);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    //结束时需要判断时候有蒙层
                    if (!showGuided) {
                        handleUserInfoGuideShow();
                    } else {
                        if (getDataBinding().flNext.getVisibility() != View.VISIBLE) {
                            getDataBinding().flNext.setVisibility(View.VISIBLE);
                        }
                    }

                }
            });
        }

    }

    /**
     * 加载匹配完成页
     */
    private void setMatchingFinishFragment() {
        MatchingRecommendFinishFragment fragment = new MatchingRecommendFinishFragment();
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_NO_DATA_DESC, getViewModel().getInfoModel().noDataDesc);
        fragment.setArguments(bundle);
        if (getDataBinding().flNext.getVisibility() == View.VISIBLE) {
            getDataBinding().flNext.setVisibility(View.GONE);
        }
        if (!getViewModel().isFirstLoadData()) {
            if (getViewModel().isClickNext()) {
                handler.removeMessages(1);
                loadingAnimator();
                getDataBinding().switcherLayout.setOnlyNextFragment(fragment, null);
            } else {
                getDataBinding().switcherLayout.setNextFragment(fragment, null);
            }
        } else {
            //是否是第一次加载
            getViewModel().setFirstLoadData(false);
            loadingAnimator();
            getDataBinding().switcherLayout.setCurrentFragment(fragment, null);
        }

        getDataBinding().refreshLayout.setEnableLoadMore(false);
        getDataBinding().refreshLayout.setEnableRefresh(true);
    }

    /**
     * 加载匹配阻断页
     */
    private void setMatchingBlockInfoFragment() {
        MatchingRecommendBlockFragment fragment = MatchingRecommendBlockFragment.getInstance(getViewModel().getInfoModel().blockInfo, false);
        if (getDataBinding().flNext.getVisibility() == View.VISIBLE) {
            getDataBinding().flNext.setVisibility(View.GONE);
        }
        if (!getViewModel().isFirstLoadData()) {
            if (getViewModel().isClickNext()) {
                handler.removeMessages(1);
                loadingAnimator();
                getDataBinding().switcherLayout.setOnlyNextFragment(fragment, null);
            } else {
                getDataBinding().switcherLayout.setNextFragment(fragment, null);
            }
        } else {
            //是否是第一次加载
            getViewModel().setFirstLoadData(false);
            loadingAnimator();
            getDataBinding().switcherLayout.setCurrentFragment(fragment, null);
        }
        getDataBinding().refreshLayout.setEnableLoadMore(false);
        getDataBinding().refreshLayout.setEnableRefresh(true);
    }

    /**
     * 加载匹配阻断页从点击喜欢按钮进入
     */
    private void setMatchingBlockInfoFragmentForLike(MatchingBlockInfoModel blockInfoModel) {
        MatchingRecommendBlockFragment fragment = MatchingRecommendBlockFragment.getInstance(blockInfoModel, true);
        if (getDataBinding().flNext.getVisibility() == View.VISIBLE) {
            getDataBinding().flNext.setVisibility(View.GONE);
        }
        getDataBinding().switcherLayout.setNextBlockFragmentForLike(fragment);
        getDataBinding().refreshLayout.setEnableLoadMore(false);
        getDataBinding().refreshLayout.setEnableRefresh(false);
    }

    /**
     * 从匹配阻断页点击稍后再说回到匹配页
     */
    private void setMatchingUserInfoFragmentForClickLater() {
        getDataBinding().switcherLayout.setBackFragmentForBlock(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                getDataBinding().refreshLayout.setEnableLoadMore(true);
                getDataBinding().refreshLayout.setEnableRefresh(true);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (getDataBinding().flNext.getVisibility() != View.VISIBLE) {
                    getDataBinding().flNext.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    /**
     * 点击喜欢后回到匹配页面
     * 举报成功后回到匹配页面
     * 不合适反馈成功后回到匹配页面
     */
    public void setMatchingUserInfoFragmentForLike() {
        if (getDataBinding().flNext.getVisibility() == View.VISIBLE) {
            getDataBinding().flNext.setVisibility(View.GONE);
        }
        getViewModel().setClickNext(true);
        getViewModel().setMatchType(MatchingViewModel.TYPE_NEXT_TYPE);
        getViewModel().loadUserInfo();
        getDataBinding().switcherLayout.setCurrentFragmentDismissForNoAnim();
        handler.sendEmptyMessageDelayed(1, 400);
    }


    /**
     * 下一个按钮动画处理
     */
    private void nextIconAnimator() {
        getDataBinding().flNext.setVisibility(View.VISIBLE);
        YoYo.with(new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "scaleX", 1.2f, 1.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 1.2f, 1.0f),
                        ObjectAnimator.ofFloat(target, "alpha", 0, 1));
            }
        }).duration(300).interpolate(InterpolatorUtil.createDefaultOvershootInterpolator()).playOn(getDataBinding().flNext);
    }

    /**
     * 匹配用户信息蒙层处理
     */
    private void handleUserInfoGuideShow() {
        ExecutorFactory.execMainTaskDelay(new Runnable() {
            @Override
            public void run() {
                if (AppPageRouter.getCurrTab(activity) != 0 || firstTabCheckedIndex != Constants.F1_RECOMMEND_INDEX) {
                    canShowGuideHasNotShow = true;
                    getDataBinding().flNext.setVisibility(View.VISIBLE);
                    return;
                }
                getDataBinding().flNext.setVisibility(View.INVISIBLE);
                showGuidePop();
            }
        }, 300);
    }

    private void showGuidePop() {
        AppPageRouter.setTabLockStatus(activity, true);
        MatchingUserInfoGuide guide = new MatchingUserInfoGuide(activity, getDataBinding().flNext);
        guide.show(new MatchingUserInfoGuide.Listener() {
            @Override
            public void secondGuideClicked() {
                getDataBinding().flNext.setVisibility(View.VISIBLE);
                AppPageRouter.setTabLockStatus(activity, false);
            }

            @Override
            public void thirdGuideClicked() {
                AppPageRouter.setTabLockStatus(activity, false);
            }
        });
        showGuided = true;
        canShowGuideHasNotShow = false;
    }

    private void initView() {
        boolean matchHide = TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_INVISIBLE), "1");
        if (matchHide) {
            setMatchHide(true);
        }
        showGuided = SpManager.get().user().getBoolean(Constants.HAS_SHOW_MATCH_USER_INFO, false);
        getDataBinding().switcherLayout.setFragmentManager(getChildFragmentManager());
        getDataBinding().refreshLayout.setOnRefreshListener(this);
        getDataBinding().refreshLayout.setOnLoadMoreListener(this);
        getDataBinding().refreshLayout.setEnableRefresh(false);
        getDataBinding().refreshLayout.setEnableLoadMore(false);

//        getDataBinding().flTitle.setOnClickListener(new OnDoubleClickListener() {
//            @Override
//            public void OnDoubleClick(View v) {
//                // 滑动到顶部
//                RxBus.getInstance().post("scrollToTop", Constants.POST_TAG_MATCHING_SCROLL_TO_TOP);
//            }
//        });
        getDataBinding().icLoading.btnRetry.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                clickError();
            }
        });
        getDataBinding().refreshFooter.setListener(new MatchingRefreshFooter.Listener() {
            @Override
            public void onRate(float rate) {
                setFooterRate(rate);
            }
        });

        ServiceManager.getInstance().getSettingService().getMatchVisibleLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (getViewModel().isMatchHide() != aBoolean) {
                    setMatchHide(aBoolean);
                }
            }
        });

//        ServiceManager.getInstance().getProfileService().getUserLiveData().observe(this, new Observer<User>() {
//            @Override
//            public void onChanged(User user) {
//                if (user != null && user.getPhase() >= Account.ACCOUNT_STATUS_FORMAL && user.getProfileLocked() == Account.PROFILE_UN_LOCKED) {
//                    if (!TextUtils.isEmpty(SettingBuilder.getInstance().getConfigurationValue(Configuration.PERSONALITY_MATCHING_H5_GAME_URL))) {
//                        setH5GameView(true);
//                    } else {
//                        setH5GameView(false);
//                    }
//                } else {
//                    setH5GameView(false);
//                }
//            }
//        });

//        ServiceManager.getInstance().getSettingService().getMatchH5GameLiveData().observe(this, new Observer<String>() {
//            @Override
//            public void onChanged(String s) {
//                if (!TextUtils.isEmpty(s)) {
//                    MutableLiveData<User> userMutableLiveData = ServiceManager.getInstance().getProfileService().getUserLiveData();
//                    if (userMutableLiveData != null) {
//                        User user = userMutableLiveData.getValue();
//                        if (user != null && user.getPhase() >= Account.ACCOUNT_STATUS_FORMAL && user.getProfileLocked() == Account.PROFILE_UN_LOCKED) {
//                            setH5GameView(true);
//                        } else {
//                            setH5GameView(false);
//                        }
//                    } else {
//                        setH5GameView(false);
//                    }
//                } else {
//                    setH5GameView(false);
//                }
//            }
//        });
    }

//    public void setH5GameView(boolean show) {
//        if (show) {
//            getDataBinding().viewH5Game.setVisibility(View.VISIBLE);
//            getDataBinding().tvTitle.setTextColor(getResources().getColor(R.color.common_color_7171F6, null));
//        } else {
//            getDataBinding().viewH5Game.setVisibility(View.GONE);
//            getDataBinding().tvTitle.setTextColor(getResources().getColor(R.color.common_black, null));
//        }
//    }

    /**
     * 是否隐藏匹配个人页
     */
    private void setMatchHide(Boolean matchHide) {
        getViewModel().setMatchHide(matchHide);
        if (matchHide) {
            getDataBinding().flLoading.setVisibility(View.GONE);
            getDataBinding().refreshLayout.setVisibility(View.GONE);
            getDataBinding().flNext.setVisibility(View.GONE);
        } else {
            getDataBinding().refreshLayout.setVisibility(View.VISIBLE);
            if (isFirstLoad) {
                isFirstLoad = false;
                fistLoadData();
            } else {
                setLoadingUI();
                getViewModel().loadUserInfo();
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!getViewModel().isMatchHide()) {
            if (isFirstLoad) {
                isFirstLoad = false;
                fistLoadData();
            } else {
                getViewModel().onResume();
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
        RxBus.getInstance().unregister(this);
    }

    /**
     * 第一次加载数据
     */
    private void fistLoadData() {
        getDataBinding().flLoading.setVisibility(View.VISIBLE);
        getDataBinding().flLoading.setAlpha(1.0f);
//        getDataBinding().icLoading.avLoad.setRepeatCount(ValueAnimator.INFINITE);
//        getDataBinding().icLoading.avLoad.playAnimation();
        getViewModel().loadUserInfo();
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.matching_fragment_matching;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getViewModel().setMatchType(MatchingViewModel.TYPE_ABOVE_TYPE);
        getViewModel().loadUserInfo();
    }

    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        getDataBinding().flNext.setVisibility(View.GONE);
        getViewModel().setMatchType(MatchingViewModel.TYPE_NEXT_TYPE);
        getViewModel().matchSkip(2);
//        getViewModel().loadUserInfo();
    }

    /**
     * 上拉加载时下一个按钮需要动画
     */
    private void setFooterRate(float rate) {
        getDataBinding().flNext.setAlpha(1 - rate);
        getDataBinding().flNext.setScaleX(1 + rate);
        getDataBinding().flNext.setScaleY(1 + rate);
    }

    @Override
    public void clickMatchNext() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        getViewModel().setClickNext(true);
        getViewModel().setMatchType(MatchingViewModel.TYPE_NEXT_TYPE);
        getViewModel().matchSkip(1);
//        getViewModel().loadUserInfo();
        getDataBinding().switcherLayout.setCurrentFragmentDismiss();
        handler.sendEmptyMessageDelayed(1, 400);

    }

    @Override
    public void clickError() {
        if (getDataBinding().icLoading.ivError.getVisibility() == View.VISIBLE) {
            setLoadingUI();
            getViewModel().loadUserInfo();
        }
    }

    @Override
    public void toH5Game() {
//        if (MultiClickUtil.isMultiClick()) {
//            return;
//        }
//        if (getDataBinding().viewH5Game.getVisibility() == View.VISIBLE) {
//            WebViewBean webViewBean = new WebViewBean();
//            webViewBean.setUrl(SettingBuilder.getInstance().getConfigurationValue(Configuration.PERSONALITY_MATCHING_H5_GAME_URL));
//            Bundle bundle = new Bundle();
//            bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean);
//            AppUtil.startUri(activity, AppPageRouter.WEB_VIEW_ACTIVITY, bundle);
//        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }
}