package com.kanzhun.marry.matching.fragment.interact.perform

import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.constant.HomeTabType
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.marry.matching.databinding.MatchingFragmentInteractWrapBinding

/**
 * 互动tab自动切换相关
 */
class InteractTabSwitchPerformance(val fragment: Fragment, val mBinding: MatchingFragmentInteractWrapBinding) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        fragment.liveEventObserve(LivedataKeyCommon.EVENT_KEY_MAIN_TAB) { it: HomeTabType ->
            when (it) {
                HomeTabType.LIKE_ME, HomeTabType.SEE_ME, HomeTabType.I_LIKE, HomeTabType.PARENT_RECOMMEND -> {
                    switchTo(it.subIndex)
                }
                else -> {

                }
            }
        }

    }

    private fun switchTo(index: Int) {
        val count = mBinding.viewPager.adapter?.itemCount ?: 0
        if (index in 0 until count) {
            mBinding.viewPager.setCurrentItem(index, false)
        }
    }
}