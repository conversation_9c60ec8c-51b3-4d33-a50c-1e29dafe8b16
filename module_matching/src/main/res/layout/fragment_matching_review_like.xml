<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MatchingReviewLikeFragment">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchingReviewLikeViewModel" />

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchingReviewListViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.MatchingReviewLikeCallback" />
    </data>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</layout>