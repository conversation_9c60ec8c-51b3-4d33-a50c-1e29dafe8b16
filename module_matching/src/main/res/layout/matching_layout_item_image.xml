<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.lihang.ShadowLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        app:clickable="false"
        tools:layout_marginVertical="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="215dp"
        android:background="@color/common_white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >

       <com.kanzhun.common.views.image.OImageView
           android:id="@+id/idImageView"
           android:scaleType="fitCenter"
           app:layout_constraintLeft_toLeftOf="parent"
           app:layout_constraintRight_toRightOf="parent"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           tools:background="@color/image_color_blue"
           android:layout_width="0dp"
           android:layout_height="0dp"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

    </com.lihang.ShadowLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
